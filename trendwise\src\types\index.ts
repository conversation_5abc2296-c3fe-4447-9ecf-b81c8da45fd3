export interface Article {
  _id?: string;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  metaDescription: string;
  metaKeywords: string[];
  ogTitle: string;
  ogDescription: string;
  ogImage?: string;
  featuredImage?: string;
  media: MediaItem[];
  tags: string[];
  category: string;
  author: string;
  publishedAt: Date;
  updatedAt: Date;
  views: number;
  isPublished: boolean;
  seoScore?: number;
}

export interface MediaItem {
  type: 'image' | 'video' | 'tweet';
  url: string;
  caption?: string;
  alt?: string;
  embedCode?: string;
}

export interface Comment {
  _id?: string;
  articleId: string;
  userId: string;
  userEmail: string;
  userName: string;
  userImage?: string;
  content: string;
  createdAt: Date;
  updatedAt: Date;
  isApproved: boolean;
  parentId?: string; // For nested comments
}

export interface User {
  _id?: string;
  email: string;
  name: string;
  image?: string;
  role: 'user' | 'admin';
  createdAt: Date;
  updatedAt: Date;
}

export interface TrendingTopic {
  keyword: string;
  searchVolume: number;
  relatedQueries: string[];
  source: 'google' | 'twitter';
  region?: string;
  timeframe?: string;
}

export interface GeneratedContent {
  title: string;
  content: string;
  metaDescription: string;
  keywords: string[];
  headings: string[];
  media: MediaItem[];
}

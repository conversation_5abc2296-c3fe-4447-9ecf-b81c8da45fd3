import mongoose, { Schema, Document } from 'mongoose';
import { Article as IArticle, MediaItem } from '@/types';

const MediaItemSchema = new Schema<MediaItem>({
  type: {
    type: String,
    enum: ['image', 'video', 'tweet'],
    required: true
  },
  url: {
    type: String,
    required: true
  },
  caption: String,
  alt: String,
  embedCode: String
});

const ArticleSchema = new Schema<IArticle & Document>({
  title: {
    type: String,
    required: true,
    trim: true
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  content: {
    type: String,
    required: true
  },
  excerpt: {
    type: String,
    required: true,
    maxlength: 300
  },
  metaDescription: {
    type: String,
    required: true,
    maxlength: 160
  },
  metaKeywords: [{
    type: String,
    trim: true
  }],
  ogTitle: {
    type: String,
    required: true
  },
  ogDescription: {
    type: String,
    required: true
  },
  ogImage: String,
  featuredImage: String,
  media: [MediaItemSchema],
  tags: [{
    type: String,
    trim: true
  }],
  category: {
    type: String,
    required: true,
    trim: true
  },
  author: {
    type: String,
    required: true,
    default: 'TrendWise AI'
  },
  publishedAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  views: {
    type: Number,
    default: 0
  },
  isPublished: {
    type: Boolean,
    default: true
  },
  seoScore: {
    type: Number,
    min: 0,
    max: 100
  }
}, {
  timestamps: true
});

// Create indexes for better performance
ArticleSchema.index({ slug: 1 });
ArticleSchema.index({ publishedAt: -1 });
ArticleSchema.index({ category: 1 });
ArticleSchema.index({ tags: 1 });
ArticleSchema.index({ title: 'text', content: 'text' });

export default mongoose.models.Article || mongoose.model<IArticle & Document>('Article', ArticleSchema);

import puppeteer from 'puppeteer';
import * as cheerio from 'cheerio';
import axios from 'axios';
import { MediaItem } from '@/types';

export interface ScrapedContent {
  title: string;
  description: string;
  images: string[];
  videos: string[];
  relatedArticles: string[];
}

export async function scrapeGoogleNews(keyword: string): Promise<ScrapedContent> {
  try {
    const searchUrl = `https://news.google.com/search?q=${encodeURIComponent(keyword)}&hl=en-US&gl=US&ceid=US:en`;
    
    const browser = await puppeteer.launch({ 
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    const page = await browser.newPage();
    
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
    await page.goto(searchUrl, { waitUntil: 'networkidle2' });
    
    const content = await page.content();
    await browser.close();
    
    const $ = cheerio.load(content);
    
    const articles: string[] = [];
    $('article h3 a').each((i, el) => {
      if (i < 5) { // Get top 5 articles
        const title = $(el).text().trim();
        if (title) articles.push(title);
      }
    });
    
    return {
      title: `Latest news about ${keyword}`,
      description: `Recent developments and news coverage about ${keyword}`,
      images: [],
      videos: [],
      relatedArticles: articles
    };
  } catch (error) {
    console.error('Error scraping Google News:', error);
    return {
      title: '',
      description: '',
      images: [],
      videos: [],
      relatedArticles: []
    };
  }
}

export async function scrapeImages(keyword: string): Promise<string[]> {
  try {
    // Using Unsplash API for free stock images
    const unsplashUrl = `https://api.unsplash.com/search/photos?query=${encodeURIComponent(keyword)}&per_page=5&client_id=demo`;
    
    const response = await axios.get(unsplashUrl);
    const images: string[] = [];
    
    if (response.data?.results) {
      response.data.results.forEach((photo: any) => {
        if (photo.urls?.regular) {
          images.push(photo.urls.regular);
        }
      });
    }
    
    return images;
  } catch (error) {
    console.error('Error fetching images:', error);
    return [];
  }
}

export async function searchYouTubeVideos(keyword: string): Promise<MediaItem[]> {
  try {
    // Note: In production, you would use YouTube Data API
    // For now, we'll return placeholder video data
    const videos: MediaItem[] = [
      {
        type: 'video',
        url: `https://www.youtube.com/results?search_query=${encodeURIComponent(keyword)}`,
        caption: `Videos about ${keyword}`,
        embedCode: `<iframe width="560" height="315" src="https://www.youtube.com/embed/search?query=${encodeURIComponent(keyword)}" frameborder="0" allowfullscreen></iframe>`
      }
    ];
    
    return videos;
  } catch (error) {
    console.error('Error searching YouTube:', error);
    return [];
  }
}

export async function extractWebsiteContent(url: string): Promise<ScrapedContent> {
  try {
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      },
      timeout: 10000
    });
    
    const $ = cheerio.load(response.data);
    
    // Extract title
    const title = $('title').text() || $('h1').first().text() || '';
    
    // Extract description
    const description = $('meta[name="description"]').attr('content') || 
                      $('meta[property="og:description"]').attr('content') || 
                      $('p').first().text().substring(0, 200) || '';
    
    // Extract images
    const images: string[] = [];
    $('img').each((i, el) => {
      const src = $(el).attr('src');
      if (src && src.startsWith('http') && i < 5) {
        images.push(src);
      }
    });
    
    return {
      title: title.trim(),
      description: description.trim(),
      images,
      videos: [],
      relatedArticles: []
    };
  } catch (error) {
    console.error('Error extracting website content:', error);
    return {
      title: '',
      description: '',
      images: [],
      videos: [],
      relatedArticles: []
    };
  }
}

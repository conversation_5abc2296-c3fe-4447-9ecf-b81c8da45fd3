{"name": "@next-auth/mongodb-adapter", "version": "1.1.3", "description": "mongoDB adapter for next-auth.", "homepage": "https://authjs.dev", "repository": "https://github.com/nextauthjs/next-auth", "bugs": {"url": "https://github.com/nextauthjs/next-auth/issues"}, "author": "Balá<PERSON>s <PERSON> <<EMAIL>>", "main": "dist/index.js", "license": "ISC", "keywords": ["next-auth", "next.js", "o<PERSON>h", "mongodb", "adapter"], "private": false, "publishConfig": {"access": "public"}, "files": ["README.md", "dist"], "peerDependencies": {"mongodb": "^5 || ^4", "next-auth": "^4"}, "devDependencies": {"jest": "^27.4.3", "mongodb": "^5.1.0", "@next-auth/adapter-test": "0.0.0", "@next-auth/tsconfig": "0.0.0", "next-auth": "4.22.1"}, "jest": {"preset": "@next-auth/adapter-test/jest"}, "scripts": {"test": "./tests/test.sh", "test:watch": "./tests/test.sh -w", "build": "tsc"}}
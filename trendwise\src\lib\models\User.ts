import mongoose, { Schema, Document } from 'mongoose';
import { User as IUser } from '@/types';

const UserSchema = new Schema<IUser & Document>({
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  image: String,
  role: {
    type: String,
    enum: ['user', 'admin'],
    default: 'user'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Create indexes
UserSchema.index({ email: 1 });

export default mongoose.models.User || mongoose.model<IUser & Document>('User', UserSchema);

{"name": "trendwise", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@next-auth/mongodb-adapter": "^1.1.3", "@types/cheerio": "^1.0.0", "axios": "^1.10.0", "cheerio": "^1.1.0", "google-trends-api": "^4.9.2", "lucide-react": "^0.525.0", "mongodb": "^6.17.0", "mongoose": "^8.16.1", "next": "15.3.4", "next-auth": "^4.24.11", "openai": "^5.8.2", "puppeteer": "^24.11.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "twitter-api-v2": "^1.24.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}
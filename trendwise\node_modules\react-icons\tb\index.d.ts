// THIS FILE IS AUTO GENERATED
import type { IconType } from '../lib/index'
export declare const TbAccessibleFilled: IconType;
export declare const TbAdCircleFilled: IconType;
export declare const TbAdFilled: IconType;
export declare const TbAdjustmentsFilled: IconType;
export declare const TbAffiliateFilled: IconType;
export declare const TbAlarmMinusFilled: IconType;
export declare const TbAlarmPlusFilled: IconType;
export declare const TbAlarmSnoozeFilled: IconType;
export declare const TbAlarmFilled: IconType;
export declare const TbAlertCircleFilled: IconType;
export declare const TbAlertHexagonFilled: IconType;
export declare const TbAlertOctagonFilled: IconType;
export declare const TbAlertSquareRoundedFilled: IconType;
export declare const TbAlertSquareFilled: IconType;
export declare const TbAlertTriangleFilled: IconType;
export declare const TbAlienFilled: IconType;
export declare const TbAlignBoxBottomCenterFilled: IconType;
export declare const TbAlignBoxBottomLeftFilled: IconType;
export declare const TbAlignBoxBottomRightFilled: IconType;
export declare const TbAlignBoxCenterMiddleFilled: IconType;
export declare const TbAlignBoxLeftBottomFilled: IconType;
export declare const TbAlignBoxLeftMiddleFilled: IconType;
export declare const TbAlignBoxLeftTopFilled: IconType;
export declare const TbAlignBoxRightBottomFilled: IconType;
export declare const TbAlignBoxRightMiddleFilled: IconType;
export declare const TbAlignBoxRightTopFilled: IconType;
export declare const TbAlignBoxTopCenterFilled: IconType;
export declare const TbAlignBoxTopLeftFilled: IconType;
export declare const TbAlignBoxTopRightFilled: IconType;
export declare const TbAnalyzeFilled: IconType;
export declare const TbAppWindowFilled: IconType;
export declare const TbAppleFilled: IconType;
export declare const TbAppsFilled: IconType;
export declare const TbArchiveFilled: IconType;
export declare const TbArrowAutofitContentFilled: IconType;
export declare const TbArrowBadgeDownFilled: IconType;
export declare const TbArrowBadgeLeftFilled: IconType;
export declare const TbArrowBadgeRightFilled: IconType;
export declare const TbArrowBadgeUpFilled: IconType;
export declare const TbArrowBigDownLineFilled: IconType;
export declare const TbArrowBigDownLinesFilled: IconType;
export declare const TbArrowBigDownFilled: IconType;
export declare const TbArrowBigLeftLineFilled: IconType;
export declare const TbArrowBigLeftLinesFilled: IconType;
export declare const TbArrowBigLeftFilled: IconType;
export declare const TbArrowBigRightLineFilled: IconType;
export declare const TbArrowBigRightLinesFilled: IconType;
export declare const TbArrowBigRightFilled: IconType;
export declare const TbArrowBigUpLineFilled: IconType;
export declare const TbArrowBigUpLinesFilled: IconType;
export declare const TbArrowBigUpFilled: IconType;
export declare const TbArrowDownCircleFilled: IconType;
export declare const TbArrowDownRhombusFilled: IconType;
export declare const TbArrowDownSquareFilled: IconType;
export declare const TbArrowGuideFilled: IconType;
export declare const TbArrowLeftCircleFilled: IconType;
export declare const TbArrowLeftRhombusFilled: IconType;
export declare const TbArrowLeftSquareFilled: IconType;
export declare const TbArrowMoveDownFilled: IconType;
export declare const TbArrowMoveLeftFilled: IconType;
export declare const TbArrowMoveRightFilled: IconType;
export declare const TbArrowMoveUpFilled: IconType;
export declare const TbArrowRightCircleFilled: IconType;
export declare const TbArrowRightRhombusFilled: IconType;
export declare const TbArrowRightSquareFilled: IconType;
export declare const TbArrowUpCircleFilled: IconType;
export declare const TbArrowUpRhombusFilled: IconType;
export declare const TbArrowUpSquareFilled: IconType;
export declare const TbArtboardFilled: IconType;
export declare const TbArticleFilled: IconType;
export declare const TbAspectRatioFilled: IconType;
export declare const TbAssemblyFilled: IconType;
export declare const TbAssetFilled: IconType;
export declare const TbAtom2Filled: IconType;
export declare const TbAutomaticGearboxFilled: IconType;
export declare const TbAwardFilled: IconType;
export declare const TbBabyCarriageFilled: IconType;
export declare const TbBackspaceFilled: IconType;
export declare const TbBadge3DFilled: IconType;
export declare const TbBadge4KFilled: IconType;
export declare const TbBadge8KFilled: IconType;
export declare const TbBadgeAdFilled: IconType;
export declare const TbBadgeArFilled: IconType;
export declare const TbBadgeCcFilled: IconType;
export declare const TbBadgeHdFilled: IconType;
export declare const TbBadgeSdFilled: IconType;
export declare const TbBadgeTmFilled: IconType;
export declare const TbBadgeVoFilled: IconType;
export declare const TbBadgeVrFilled: IconType;
export declare const TbBadgeWcFilled: IconType;
export declare const TbBadgeFilled: IconType;
export declare const TbBadgesFilled: IconType;
export declare const TbBalloonFilled: IconType;
export declare const TbBallpenFilled: IconType;
export declare const TbBandageFilled: IconType;
export declare const TbBarbellFilled: IconType;
export declare const TbBarrierBlockFilled: IconType;
export declare const TbBasketFilled: IconType;
export declare const TbBathFilled: IconType;
export declare const TbBattery1Filled: IconType;
export declare const TbBattery2Filled: IconType;
export declare const TbBattery3Filled: IconType;
export declare const TbBattery4Filled: IconType;
export declare const TbBatteryAutomotiveFilled: IconType;
export declare const TbBatteryVertical1Filled: IconType;
export declare const TbBatteryVertical2Filled: IconType;
export declare const TbBatteryVertical3Filled: IconType;
export declare const TbBatteryVertical4Filled: IconType;
export declare const TbBatteryVerticalFilled: IconType;
export declare const TbBatteryFilled: IconType;
export declare const TbBedFlatFilled: IconType;
export declare const TbBedFilled: IconType;
export declare const TbBeerFilled: IconType;
export declare const TbBellMinusFilled: IconType;
export declare const TbBellPlusFilled: IconType;
export declare const TbBellRinging2Filled: IconType;
export declare const TbBellRingingFilled: IconType;
export declare const TbBellXFilled: IconType;
export declare const TbBellZFilled: IconType;
export declare const TbBellFilled: IconType;
export declare const TbBikeFilled: IconType;
export declare const TbBinaryTree2Filled: IconType;
export declare const TbBinaryTreeFilled: IconType;
export declare const TbBinocularsFilled: IconType;
export declare const TbBiohazardFilled: IconType;
export declare const TbBladeFilled: IconType;
export declare const TbBlenderFilled: IconType;
export declare const TbBlobFilled: IconType;
export declare const TbBoltFilled: IconType;
export declare const TbBombFilled: IconType;
export declare const TbBoneFilled: IconType;
export declare const TbBongFilled: IconType;
export declare const TbBookFilled: IconType;
export declare const TbBookmarkFilled: IconType;
export declare const TbBookmarksFilled: IconType;
export declare const TbBoomFilled: IconType;
export declare const TbBottleFilled: IconType;
export declare const TbBounceLeftFilled: IconType;
export declare const TbBounceRightFilled: IconType;
export declare const TbBowFilled: IconType;
export declare const TbBowlChopsticksFilled: IconType;
export declare const TbBowlSpoonFilled: IconType;
export declare const TbBowlFilled: IconType;
export declare const TbBoxAlignBottomLeftFilled: IconType;
export declare const TbBoxAlignBottomRightFilled: IconType;
export declare const TbBoxAlignBottomFilled: IconType;
export declare const TbBoxAlignLeftFilled: IconType;
export declare const TbBoxAlignRightFilled: IconType;
export declare const TbBoxAlignTopLeftFilled: IconType;
export declare const TbBoxAlignTopRightFilled: IconType;
export declare const TbBoxAlignTopFilled: IconType;
export declare const TbBoxMultipleFilled: IconType;
export declare const TbBrandAngularFilled: IconType;
export declare const TbBrandAppleFilled: IconType;
export declare const TbBrandBitbucketFilled: IconType;
export declare const TbBrandDiscordFilled: IconType;
export declare const TbBrandDribbbleFilled: IconType;
export declare const TbBrandFacebookFilled: IconType;
export declare const TbBrandGithubFilled: IconType;
export declare const TbBrandGoogleFilled: IconType;
export declare const TbBrandInstagramFilled: IconType;
export declare const TbBrandKickFilled: IconType;
export declare const TbBrandLinkedinFilled: IconType;
export declare const TbBrandMessengerFilled: IconType;
export declare const TbBrandOpenSourceFilled: IconType;
export declare const TbBrandOperaFilled: IconType;
export declare const TbBrandPatreonFilled: IconType;
export declare const TbBrandPaypalFilled: IconType;
export declare const TbBrandPinterestFilled: IconType;
export declare const TbBrandSketchFilled: IconType;
export declare const TbBrandSnapchatFilled: IconType;
export declare const TbBrandSpotifyFilled: IconType;
export declare const TbBrandSteamFilled: IconType;
export declare const TbBrandStripeFilled: IconType;
export declare const TbBrandTablerFilled: IconType;
export declare const TbBrandTiktokFilled: IconType;
export declare const TbBrandTinderFilled: IconType;
export declare const TbBrandTumblrFilled: IconType;
export declare const TbBrandTwitterFilled: IconType;
export declare const TbBrandVercelFilled: IconType;
export declare const TbBrandVimeoFilled: IconType;
export declare const TbBrandWeiboFilled: IconType;
export declare const TbBrandWhatsappFilled: IconType;
export declare const TbBrandWindowsFilled: IconType;
export declare const TbBrandXFilled: IconType;
export declare const TbBrandYoutubeFilled: IconType;
export declare const TbBreadFilled: IconType;
export declare const TbBriefcase2Filled: IconType;
export declare const TbBriefcaseFilled: IconType;
export declare const TbBrightnessAutoFilled: IconType;
export declare const TbBrightnessDownFilled: IconType;
export declare const TbBrightnessUpFilled: IconType;
export declare const TbBrightnessFilled: IconType;
export declare const TbBubbleTextFilled: IconType;
export declare const TbBubbleFilled: IconType;
export declare const TbBugFilled: IconType;
export declare const TbBuildingBroadcastTowerFilled: IconType;
export declare const TbBulbFilled: IconType;
export declare const TbButterflyFilled: IconType;
export declare const TbCactusFilled: IconType;
export declare const TbCalculatorFilled: IconType;
export declare const TbCalendarEventFilled: IconType;
export declare const TbCalendarMonthFilled: IconType;
export declare const TbCalendarWeekFilled: IconType;
export declare const TbCalendarFilled: IconType;
export declare const TbCameraFilled: IconType;
export declare const TbCampfireFilled: IconType;
export declare const TbCandleFilled: IconType;
export declare const TbCannabisFilled: IconType;
export declare const TbCapsuleHorizontalFilled: IconType;
export declare const TbCapsuleFilled: IconType;
export declare const TbCaptureFilled: IconType;
export declare const TbCar4WdFilled: IconType;
export declare const TbCarFanFilled: IconType;
export declare const TbCarSuvFilled: IconType;
export declare const TbCarFilled: IconType;
export declare const TbCarambolaFilled: IconType;
export declare const TbCardboardsFilled: IconType;
export declare const TbCardsFilled: IconType;
export declare const TbCaretDownFilled: IconType;
export declare const TbCaretLeftRightFilled: IconType;
export declare const TbCaretLeftFilled: IconType;
export declare const TbCaretRightFilled: IconType;
export declare const TbCaretUpDownFilled: IconType;
export declare const TbCaretUpFilled: IconType;
export declare const TbCarouselHorizontalFilled: IconType;
export declare const TbCarouselVerticalFilled: IconType;
export declare const TbCashBanknoteFilled: IconType;
export declare const TbCategoryFilled: IconType;
export declare const TbChargingPileFilled: IconType;
export declare const TbChartAreaLineFilled: IconType;
export declare const TbChartAreaFilled: IconType;
export declare const TbChartBubbleFilled: IconType;
export declare const TbChartCandleFilled: IconType;
export declare const TbChartDonutFilled: IconType;
export declare const TbChartDotsFilled: IconType;
export declare const TbChartGridDotsFilled: IconType;
export declare const TbChartPieFilled: IconType;
export declare const TbCherryFilled: IconType;
export declare const TbChessBishopFilled: IconType;
export declare const TbChessKingFilled: IconType;
export declare const TbChessKnightFilled: IconType;
export declare const TbChessQueenFilled: IconType;
export declare const TbChessRookFilled: IconType;
export declare const TbChessFilled: IconType;
export declare const TbChristmasTreeFilled: IconType;
export declare const TbCircleArrowDownLeftFilled: IconType;
export declare const TbCircleArrowDownRightFilled: IconType;
export declare const TbCircleArrowDownFilled: IconType;
export declare const TbCircleArrowLeftFilled: IconType;
export declare const TbCircleArrowRightFilled: IconType;
export declare const TbCircleArrowUpLeftFilled: IconType;
export declare const TbCircleArrowUpRightFilled: IconType;
export declare const TbCircleArrowUpFilled: IconType;
export declare const TbCircleCheckFilled: IconType;
export declare const TbCircleDotFilled: IconType;
export declare const TbCircleKeyFilled: IconType;
export declare const TbCircleLetterAFilled: IconType;
export declare const TbCircleLetterBFilled: IconType;
export declare const TbCircleLetterCFilled: IconType;
export declare const TbCircleLetterDFilled: IconType;
export declare const TbCircleLetterEFilled: IconType;
export declare const TbCircleLetterFFilled: IconType;
export declare const TbCircleLetterGFilled: IconType;
export declare const TbCircleLetterHFilled: IconType;
export declare const TbCircleLetterIFilled: IconType;
export declare const TbCircleLetterJFilled: IconType;
export declare const TbCircleLetterKFilled: IconType;
export declare const TbCircleLetterLFilled: IconType;
export declare const TbCircleLetterMFilled: IconType;
export declare const TbCircleLetterNFilled: IconType;
export declare const TbCircleLetterOFilled: IconType;
export declare const TbCircleLetterPFilled: IconType;
export declare const TbCircleLetterQFilled: IconType;
export declare const TbCircleLetterRFilled: IconType;
export declare const TbCircleLetterSFilled: IconType;
export declare const TbCircleLetterTFilled: IconType;
export declare const TbCircleLetterUFilled: IconType;
export declare const TbCircleLetterVFilled: IconType;
export declare const TbCircleLetterWFilled: IconType;
export declare const TbCircleLetterXFilled: IconType;
export declare const TbCircleLetterYFilled: IconType;
export declare const TbCircleLetterZFilled: IconType;
export declare const TbCircleNumber0Filled: IconType;
export declare const TbCircleNumber1Filled: IconType;
export declare const TbCircleNumber2Filled: IconType;
export declare const TbCircleNumber3Filled: IconType;
export declare const TbCircleNumber4Filled: IconType;
export declare const TbCircleNumber5Filled: IconType;
export declare const TbCircleNumber6Filled: IconType;
export declare const TbCircleNumber7Filled: IconType;
export declare const TbCircleNumber8Filled: IconType;
export declare const TbCircleNumber9Filled: IconType;
export declare const TbCirclePercentageFilled: IconType;
export declare const TbCirclePlusFilled: IconType;
export declare const TbCircleRectangleFilled: IconType;
export declare const TbCircleXFilled: IconType;
export declare const TbCircleFilled: IconType;
export declare const TbCirclesFilled: IconType;
export declare const TbClockHour1Filled: IconType;
export declare const TbClockHour10Filled: IconType;
export declare const TbClockHour11Filled: IconType;
export declare const TbClockHour12Filled: IconType;
export declare const TbClockHour2Filled: IconType;
export declare const TbClockHour3Filled: IconType;
export declare const TbClockHour4Filled: IconType;
export declare const TbClockHour5Filled: IconType;
export declare const TbClockHour6Filled: IconType;
export declare const TbClockHour7Filled: IconType;
export declare const TbClockHour8Filled: IconType;
export declare const TbClockHour9Filled: IconType;
export declare const TbClockFilled: IconType;
export declare const TbCloudFilled: IconType;
export declare const TbCloverFilled: IconType;
export declare const TbClubsFilled: IconType;
export declare const TbCodeCircle2Filled: IconType;
export declare const TbCodeCircleFilled: IconType;
export declare const TbCoinBitcoinFilled: IconType;
export declare const TbCoinEuroFilled: IconType;
export declare const TbCoinMoneroFilled: IconType;
export declare const TbCoinPoundFilled: IconType;
export declare const TbCoinRupeeFilled: IconType;
export declare const TbCoinTakaFilled: IconType;
export declare const TbCoinYenFilled: IconType;
export declare const TbCoinYuanFilled: IconType;
export declare const TbCoinFilled: IconType;
export declare const TbCompassFilled: IconType;
export declare const TbCone2Filled: IconType;
export declare const TbConeFilled: IconType;
export declare const TbContrast2Filled: IconType;
export declare const TbContrastFilled: IconType;
export declare const TbCookieManFilled: IconType;
export declare const TbCookieFilled: IconType;
export declare const TbCopyCheckFilled: IconType;
export declare const TbCopyMinusFilled: IconType;
export declare const TbCopyPlusFilled: IconType;
export declare const TbCopyXFilled: IconType;
export declare const TbCopyleftFilled: IconType;
export declare const TbCopyrightFilled: IconType;
export declare const TbCreditCardFilled: IconType;
export declare const TbCrop11Filled: IconType;
export declare const TbCrop169Filled: IconType;
export declare const TbCrop32Filled: IconType;
export declare const TbCrop54Filled: IconType;
export declare const TbCrop75Filled: IconType;
export declare const TbCropLandscapeFilled: IconType;
export declare const TbCropPortraitFilled: IconType;
export declare const TbCrossFilled: IconType;
export declare const TbDashboardFilled: IconType;
export declare const TbDeviceCctvFilled: IconType;
export declare const TbDeviceDesktopFilled: IconType;
export declare const TbDeviceGamepad3Filled: IconType;
export declare const TbDeviceHeartMonitorFilled: IconType;
export declare const TbDeviceImacFilled: IconType;
export declare const TbDeviceIpadFilled: IconType;
export declare const TbDeviceMobileFilled: IconType;
export declare const TbDeviceRemoteFilled: IconType;
export declare const TbDeviceSpeakerFilled: IconType;
export declare const TbDeviceTabletFilled: IconType;
export declare const TbDeviceTvOldFilled: IconType;
export declare const TbDeviceTvFilled: IconType;
export declare const TbDeviceUnknownFilled: IconType;
export declare const TbDeviceUsbFilled: IconType;
export declare const TbDeviceVisionProFilled: IconType;
export declare const TbDeviceWatchFilled: IconType;
export declare const TbDialpadFilled: IconType;
export declare const TbDiamondFilled: IconType;
export declare const TbDiamondsFilled: IconType;
export declare const TbDice1Filled: IconType;
export declare const TbDice2Filled: IconType;
export declare const TbDice3Filled: IconType;
export declare const TbDice4Filled: IconType;
export declare const TbDice5Filled: IconType;
export declare const TbDice6Filled: IconType;
export declare const TbDiceFilled: IconType;
export declare const TbDirectionArrowsFilled: IconType;
export declare const TbDirectionSignFilled: IconType;
export declare const TbDirectionsFilled: IconType;
export declare const TbDiscFilled: IconType;
export declare const TbDiscountFilled: IconType;
export declare const TbDropletHalf2Filled: IconType;
export declare const TbDropletHalfFilled: IconType;
export declare const TbDropletFilled: IconType;
export declare const TbDropletsFilled: IconType;
export declare const TbEggCrackedFilled: IconType;
export declare const TbEggFilled: IconType;
export declare const TbElevatorFilled: IconType;
export declare const TbExclamationCircleFilled: IconType;
export declare const TbEyeFilled: IconType;
export declare const TbEyeglassFilled: IconType;
export declare const TbFaviconFilled: IconType;
export declare const TbFeatherFilled: IconType;
export declare const TbFileXFilled: IconType;
export declare const TbFileFilled: IconType;
export declare const TbFilterFilled: IconType;
export declare const TbFiltersFilled: IconType;
export declare const TbFlag2Filled: IconType;
export declare const TbFlag3Filled: IconType;
export declare const TbFlagFilled: IconType;
export declare const TbFlameFilled: IconType;
export declare const TbFlareFilled: IconType;
export declare const TbFlask2Filled: IconType;
export declare const TbFlaskFilled: IconType;
export declare const TbFlowerFilled: IconType;
export declare const TbFolderFilled: IconType;
export declare const TbFoldersFilled: IconType;
export declare const TbForbid2Filled: IconType;
export declare const TbForbidFilled: IconType;
export declare const TbFountainFilled: IconType;
export declare const TbFunctionFilled: IconType;
export declare const TbGardenCartFilled: IconType;
export declare const TbGaugeFilled: IconType;
export declare const TbGhost2Filled: IconType;
export declare const TbGhost3Filled: IconType;
export declare const TbGhostFilled: IconType;
export declare const TbGiftCardFilled: IconType;
export declare const TbGiftFilled: IconType;
export declare const TbGlassFullFilled: IconType;
export declare const TbGlassFilled: IconType;
export declare const TbGlobeFilled: IconType;
export declare const TbGolfFilled: IconType;
export declare const TbGpsFilled: IconType;
export declare const TbGraphFilled: IconType;
export declare const TbGridPatternFilled: IconType;
export declare const TbGuitarPickFilled: IconType;
export declare const TbHanger2Filled: IconType;
export declare const TbHeadphonesFilled: IconType;
export declare const TbHeartFilled: IconType;
export declare const TbHelicopterLandingFilled: IconType;
export declare const TbHelpCircleFilled: IconType;
export declare const TbHelpHexagonFilled: IconType;
export declare const TbHelpOctagonFilled: IconType;
export declare const TbHelpSquareRoundedFilled: IconType;
export declare const TbHelpSquareFilled: IconType;
export declare const TbHelpTriangleFilled: IconType;
export declare const TbHexagonLetterAFilled: IconType;
export declare const TbHexagonLetterBFilled: IconType;
export declare const TbHexagonLetterCFilled: IconType;
export declare const TbHexagonLetterDFilled: IconType;
export declare const TbHexagonLetterEFilled: IconType;
export declare const TbHexagonLetterFFilled: IconType;
export declare const TbHexagonLetterGFilled: IconType;
export declare const TbHexagonLetterHFilled: IconType;
export declare const TbHexagonLetterIFilled: IconType;
export declare const TbHexagonLetterJFilled: IconType;
export declare const TbHexagonLetterKFilled: IconType;
export declare const TbHexagonLetterLFilled: IconType;
export declare const TbHexagonLetterMFilled: IconType;
export declare const TbHexagonLetterNFilled: IconType;
export declare const TbHexagonLetterOFilled: IconType;
export declare const TbHexagonLetterPFilled: IconType;
export declare const TbHexagonLetterQFilled: IconType;
export declare const TbHexagonLetterRFilled: IconType;
export declare const TbHexagonLetterSFilled: IconType;
export declare const TbHexagonLetterTFilled: IconType;
export declare const TbHexagonLetterUFilled: IconType;
export declare const TbHexagonLetterVFilled: IconType;
export declare const TbHexagonLetterWFilled: IconType;
export declare const TbHexagonLetterXFilled: IconType;
export declare const TbHexagonLetterYFilled: IconType;
export declare const TbHexagonLetterZFilled: IconType;
export declare const TbHexagonMinusFilled: IconType;
export declare const TbHexagonNumber0Filled: IconType;
export declare const TbHexagonNumber1Filled: IconType;
export declare const TbHexagonNumber2Filled: IconType;
export declare const TbHexagonNumber3Filled: IconType;
export declare const TbHexagonNumber4Filled: IconType;
export declare const TbHexagonNumber5Filled: IconType;
export declare const TbHexagonNumber6Filled: IconType;
export declare const TbHexagonNumber7Filled: IconType;
export declare const TbHexagonNumber8Filled: IconType;
export declare const TbHexagonNumber9Filled: IconType;
export declare const TbHexagonPlusFilled: IconType;
export declare const TbHexagonFilled: IconType;
export declare const TbHomeFilled: IconType;
export declare const TbHospitalCircleFilled: IconType;
export declare const TbHourglassFilled: IconType;
export declare const TbIconsFilled: IconType;
export declare const TbInfoCircleFilled: IconType;
export declare const TbInfoHexagonFilled: IconType;
export declare const TbInfoOctagonFilled: IconType;
export declare const TbInfoSquareRoundedFilled: IconType;
export declare const TbInfoSquareFilled: IconType;
export declare const TbInfoTriangleFilled: IconType;
export declare const TbInnerShadowBottomLeftFilled: IconType;
export declare const TbInnerShadowBottomRightFilled: IconType;
export declare const TbInnerShadowBottomFilled: IconType;
export declare const TbInnerShadowLeftFilled: IconType;
export declare const TbInnerShadowRightFilled: IconType;
export declare const TbInnerShadowTopLeftFilled: IconType;
export declare const TbInnerShadowTopRightFilled: IconType;
export declare const TbInnerShadowTopFilled: IconType;
export declare const TbIroning1Filled: IconType;
export declare const TbIroning2Filled: IconType;
export declare const TbIroning3Filled: IconType;
export declare const TbIroningSteamFilled: IconType;
export declare const TbIroningFilled: IconType;
export declare const TbJetpackFilled: IconType;
export declare const TbJewishStarFilled: IconType;
export declare const TbKeyFilled: IconType;
export declare const TbKeyboardFilled: IconType;
export declare const TbKeyframeAlignCenterFilled: IconType;
export declare const TbKeyframeAlignHorizontalFilled: IconType;
export declare const TbKeyframeAlignVerticalFilled: IconType;
export declare const TbKeyframeFilled: IconType;
export declare const TbKeyframesFilled: IconType;
export declare const TbLabelImportantFilled: IconType;
export declare const TbLabelFilled: IconType;
export declare const TbLassoPolygonFilled: IconType;
export declare const TbLaurelWreathFilled: IconType;
export declare const TbLayout2Filled: IconType;
export declare const TbLayoutAlignBottomFilled: IconType;
export declare const TbLayoutAlignCenterFilled: IconType;
export declare const TbLayoutAlignLeftFilled: IconType;
export declare const TbLayoutAlignMiddleFilled: IconType;
export declare const TbLayoutAlignRightFilled: IconType;
export declare const TbLayoutAlignTopFilled: IconType;
export declare const TbLayoutBottombarCollapseFilled: IconType;
export declare const TbLayoutBottombarExpandFilled: IconType;
export declare const TbLayoutBottombarFilled: IconType;
export declare const TbLayoutCardsFilled: IconType;
export declare const TbLayoutDashboardFilled: IconType;
export declare const TbLayoutDistributeHorizontalFilled: IconType;
export declare const TbLayoutDistributeVerticalFilled: IconType;
export declare const TbLayoutGridFilled: IconType;
export declare const TbLayoutKanbanFilled: IconType;
export declare const TbLayoutListFilled: IconType;
export declare const TbLayoutNavbarCollapseFilled: IconType;
export declare const TbLayoutNavbarExpandFilled: IconType;
export declare const TbLayoutNavbarFilled: IconType;
export declare const TbLayoutSidebarLeftCollapseFilled: IconType;
export declare const TbLayoutSidebarLeftExpandFilled: IconType;
export declare const TbLayoutSidebarRightCollapseFilled: IconType;
export declare const TbLayoutSidebarRightExpandFilled: IconType;
export declare const TbLayoutSidebarRightFilled: IconType;
export declare const TbLayoutSidebarFilled: IconType;
export declare const TbLayoutFilled: IconType;
export declare const TbLegoFilled: IconType;
export declare const TbLemon2Filled: IconType;
export declare const TbLifebuoyFilled: IconType;
export declare const TbLivePhotoFilled: IconType;
export declare const TbLiveViewFilled: IconType;
export declare const TbLocationFilled: IconType;
export declare const TbLockSquareRoundedFilled: IconType;
export declare const TbLockFilled: IconType;
export declare const TbLungsFilled: IconType;
export declare const TbMacroFilled: IconType;
export declare const TbMagnetFilled: IconType;
export declare const TbMailOpenedFilled: IconType;
export declare const TbMailFilled: IconType;
export declare const TbManFilled: IconType;
export declare const TbManualGearboxFilled: IconType;
export declare const TbMapPinFilled: IconType;
export declare const TbMedicalCrossFilled: IconType;
export declare const TbMeepleFilled: IconType;
export declare const TbMelonFilled: IconType;
export declare const TbMessage2Filled: IconType;
export declare const TbMessageChatbotFilled: IconType;
export declare const TbMessageCircleFilled: IconType;
export declare const TbMessageReportFilled: IconType;
export declare const TbMessageFilled: IconType;
export declare const TbMeteorFilled: IconType;
export declare const TbMichelinStarFilled: IconType;
export declare const TbMickeyFilled: IconType;
export declare const TbMicrophoneFilled: IconType;
export declare const TbMicrowaveFilled: IconType;
export declare const TbMilitaryRankFilled: IconType;
export declare const TbMilkFilled: IconType;
export declare const TbMoodAngryFilled: IconType;
export declare const TbMoodConfuzedFilled: IconType;
export declare const TbMoodCrazyHappyFilled: IconType;
export declare const TbMoodEmptyFilled: IconType;
export declare const TbMoodHappyFilled: IconType;
export declare const TbMoodKidFilled: IconType;
export declare const TbMoodNeutralFilled: IconType;
export declare const TbMoodSadFilled: IconType;
export declare const TbMoodSmileFilled: IconType;
export declare const TbMoodWrrrFilled: IconType;
export declare const TbMoonFilled: IconType;
export declare const TbMountainFilled: IconType;
export declare const TbMouseFilled: IconType;
export declare const TbMugFilled: IconType;
export declare const TbMushroomFilled: IconType;
export declare const TbNavigationFilled: IconType;
export declare const TbNurseFilled: IconType;
export declare const TbOctagonFilled: IconType;
export declare const TbOvalVerticalFilled: IconType;
export declare const TbOvalFilled: IconType;
export declare const TbPaintFilled: IconType;
export declare const TbPaletteFilled: IconType;
export declare const TbPanoramaHorizontalFilled: IconType;
export declare const TbPanoramaVerticalFilled: IconType;
export declare const TbParkingCircleFilled: IconType;
export declare const TbPawFilled: IconType;
export declare const TbPennant2Filled: IconType;
export declare const TbPennantFilled: IconType;
export declare const TbPentagonFilled: IconType;
export declare const TbPhoneFilled: IconType;
export declare const TbPhotoFilled: IconType;
export declare const TbPictureInPictureTopFilled: IconType;
export declare const TbPictureInPictureFilled: IconType;
export declare const TbPillFilled: IconType;
export declare const TbPinFilled: IconType;
export declare const TbPinnedFilled: IconType;
export declare const TbPizzaFilled: IconType;
export declare const TbPlayCard1Filled: IconType;
export declare const TbPlayCard10Filled: IconType;
export declare const TbPlayCard2Filled: IconType;
export declare const TbPlayCard3Filled: IconType;
export declare const TbPlayCard4Filled: IconType;
export declare const TbPlayCard5Filled: IconType;
export declare const TbPlayCard6Filled: IconType;
export declare const TbPlayCard7Filled: IconType;
export declare const TbPlayCard8Filled: IconType;
export declare const TbPlayCard9Filled: IconType;
export declare const TbPlayCardAFilled: IconType;
export declare const TbPlayCardJFilled: IconType;
export declare const TbPlayCardKFilled: IconType;
export declare const TbPlayCardQFilled: IconType;
export declare const TbPlayCardStarFilled: IconType;
export declare const TbPlayerEjectFilled: IconType;
export declare const TbPlayerPauseFilled: IconType;
export declare const TbPlayerPlayFilled: IconType;
export declare const TbPlayerRecordFilled: IconType;
export declare const TbPlayerSkipBackFilled: IconType;
export declare const TbPlayerSkipForwardFilled: IconType;
export declare const TbPlayerStopFilled: IconType;
export declare const TbPlayerTrackNextFilled: IconType;
export declare const TbPlayerTrackPrevFilled: IconType;
export declare const TbPointFilled: IconType;
export declare const TbPointerFilled: IconType;
export declare const TbPolaroidFilled: IconType;
export declare const TbPooFilled: IconType;
export declare const TbPresentationAnalyticsFilled: IconType;
export declare const TbPresentationFilled: IconType;
export declare const TbPuzzleFilled: IconType;
export declare const TbQuoteFilled: IconType;
export declare const TbRadarFilled: IconType;
export declare const TbRadioactiveFilled: IconType;
export declare const TbReceiptFilled: IconType;
export declare const TbRectangleVerticalFilled: IconType;
export declare const TbRectangleFilled: IconType;
export declare const TbRelationManyToManyFilled: IconType;
export declare const TbRelationOneToManyFilled: IconType;
export declare const TbRelationOneToOneFilled: IconType;
export declare const TbReplaceFilled: IconType;
export declare const TbRosetteDiscountCheckFilled: IconType;
export declare const TbRosetteDiscountFilled: IconType;
export declare const TbRosetteFilled: IconType;
export declare const TbSaladFilled: IconType;
export declare const TbScubaDivingTankFilled: IconType;
export declare const TbSectionFilled: IconType;
export declare const TbSeedingFilled: IconType;
export declare const TbSettingsFilled: IconType;
export declare const TbShieldCheckFilled: IconType;
export declare const TbShieldCheckeredFilled: IconType;
export declare const TbShieldHalfFilled: IconType;
export declare const TbShieldLockFilled: IconType;
export declare const TbShieldFilled: IconType;
export declare const TbShirtFilled: IconType;
export declare const TbShoppingCartFilled: IconType;
export declare const TbSignLeftFilled: IconType;
export declare const TbSignRightFilled: IconType;
export declare const TbSitemapFilled: IconType;
export declare const TbSortAscending2Filled: IconType;
export declare const TbSortAscendingShapesFilled: IconType;
export declare const TbSortDescending2Filled: IconType;
export declare const TbSortDescendingShapesFilled: IconType;
export declare const TbSoupFilled: IconType;
export declare const TbSpadeFilled: IconType;
export declare const TbSquareArrowDownFilled: IconType;
export declare const TbSquareArrowLeftFilled: IconType;
export declare const TbSquareArrowRightFilled: IconType;
export declare const TbSquareArrowUpFilled: IconType;
export declare const TbSquareAsteriskFilled: IconType;
export declare const TbSquareCheckFilled: IconType;
export declare const TbSquareChevronDownFilled: IconType;
export declare const TbSquareChevronLeftFilled: IconType;
export declare const TbSquareChevronRightFilled: IconType;
export declare const TbSquareChevronUpFilled: IconType;
export declare const TbSquareChevronsDownFilled: IconType;
export declare const TbSquareChevronsLeftFilled: IconType;
export declare const TbSquareChevronsRightFilled: IconType;
export declare const TbSquareChevronsUpFilled: IconType;
export declare const TbSquareDotFilled: IconType;
export declare const TbSquareF0Filled: IconType;
export declare const TbSquareF1Filled: IconType;
export declare const TbSquareF2Filled: IconType;
export declare const TbSquareF3Filled: IconType;
export declare const TbSquareF4Filled: IconType;
export declare const TbSquareF5Filled: IconType;
export declare const TbSquareF6Filled: IconType;
export declare const TbSquareF7Filled: IconType;
export declare const TbSquareF8Filled: IconType;
export declare const TbSquareF9Filled: IconType;
export declare const TbSquareLetterAFilled: IconType;
export declare const TbSquareLetterBFilled: IconType;
export declare const TbSquareLetterCFilled: IconType;
export declare const TbSquareLetterDFilled: IconType;
export declare const TbSquareLetterEFilled: IconType;
export declare const TbSquareLetterFFilled: IconType;
export declare const TbSquareLetterGFilled: IconType;
export declare const TbSquareLetterHFilled: IconType;
export declare const TbSquareLetterIFilled: IconType;
export declare const TbSquareLetterJFilled: IconType;
export declare const TbSquareLetterKFilled: IconType;
export declare const TbSquareLetterLFilled: IconType;
export declare const TbSquareLetterMFilled: IconType;
export declare const TbSquareLetterNFilled: IconType;
export declare const TbSquareLetterOFilled: IconType;
export declare const TbSquareLetterPFilled: IconType;
export declare const TbSquareLetterQFilled: IconType;
export declare const TbSquareLetterRFilled: IconType;
export declare const TbSquareLetterSFilled: IconType;
export declare const TbSquareLetterTFilled: IconType;
export declare const TbSquareLetterUFilled: IconType;
export declare const TbSquareLetterVFilled: IconType;
export declare const TbSquareLetterWFilled: IconType;
export declare const TbSquareLetterXFilled: IconType;
export declare const TbSquareLetterYFilled: IconType;
export declare const TbSquareLetterZFilled: IconType;
export declare const TbSquareMinusFilled: IconType;
export declare const TbSquareNumber0Filled: IconType;
export declare const TbSquareNumber1Filled: IconType;
export declare const TbSquareNumber2Filled: IconType;
export declare const TbSquareNumber3Filled: IconType;
export declare const TbSquareNumber4Filled: IconType;
export declare const TbSquareNumber5Filled: IconType;
export declare const TbSquareNumber6Filled: IconType;
export declare const TbSquareNumber7Filled: IconType;
export declare const TbSquareNumber8Filled: IconType;
export declare const TbSquareNumber9Filled: IconType;
export declare const TbSquareRotatedFilled: IconType;
export declare const TbSquareRoundedArrowDownFilled: IconType;
export declare const TbSquareRoundedArrowLeftFilled: IconType;
export declare const TbSquareRoundedArrowRightFilled: IconType;
export declare const TbSquareRoundedArrowUpFilled: IconType;
export declare const TbSquareRoundedCheckFilled: IconType;
export declare const TbSquareRoundedChevronDownFilled: IconType;
export declare const TbSquareRoundedChevronLeftFilled: IconType;
export declare const TbSquareRoundedChevronRightFilled: IconType;
export declare const TbSquareRoundedChevronUpFilled: IconType;
export declare const TbSquareRoundedChevronsDownFilled: IconType;
export declare const TbSquareRoundedChevronsLeftFilled: IconType;
export declare const TbSquareRoundedChevronsRightFilled: IconType;
export declare const TbSquareRoundedChevronsUpFilled: IconType;
export declare const TbSquareRoundedLetterAFilled: IconType;
export declare const TbSquareRoundedLetterBFilled: IconType;
export declare const TbSquareRoundedLetterCFilled: IconType;
export declare const TbSquareRoundedLetterDFilled: IconType;
export declare const TbSquareRoundedLetterEFilled: IconType;
export declare const TbSquareRoundedLetterFFilled: IconType;
export declare const TbSquareRoundedLetterGFilled: IconType;
export declare const TbSquareRoundedLetterHFilled: IconType;
export declare const TbSquareRoundedLetterIFilled: IconType;
export declare const TbSquareRoundedLetterJFilled: IconType;
export declare const TbSquareRoundedLetterKFilled: IconType;
export declare const TbSquareRoundedLetterLFilled: IconType;
export declare const TbSquareRoundedLetterMFilled: IconType;
export declare const TbSquareRoundedLetterNFilled: IconType;
export declare const TbSquareRoundedLetterOFilled: IconType;
export declare const TbSquareRoundedLetterPFilled: IconType;
export declare const TbSquareRoundedLetterQFilled: IconType;
export declare const TbSquareRoundedLetterRFilled: IconType;
export declare const TbSquareRoundedLetterSFilled: IconType;
export declare const TbSquareRoundedLetterTFilled: IconType;
export declare const TbSquareRoundedLetterUFilled: IconType;
export declare const TbSquareRoundedLetterVFilled: IconType;
export declare const TbSquareRoundedLetterWFilled: IconType;
export declare const TbSquareRoundedLetterXFilled: IconType;
export declare const TbSquareRoundedLetterYFilled: IconType;
export declare const TbSquareRoundedLetterZFilled: IconType;
export declare const TbSquareRoundedMinusFilled: IconType;
export declare const TbSquareRoundedNumber0Filled: IconType;
export declare const TbSquareRoundedNumber1Filled: IconType;
export declare const TbSquareRoundedNumber2Filled: IconType;
export declare const TbSquareRoundedNumber3Filled: IconType;
export declare const TbSquareRoundedNumber4Filled: IconType;
export declare const TbSquareRoundedNumber5Filled: IconType;
export declare const TbSquareRoundedNumber6Filled: IconType;
export declare const TbSquareRoundedNumber7Filled: IconType;
export declare const TbSquareRoundedNumber8Filled: IconType;
export declare const TbSquareRoundedNumber9Filled: IconType;
export declare const TbSquareRoundedPlusFilled: IconType;
export declare const TbSquareRoundedXFilled: IconType;
export declare const TbSquareRoundedFilled: IconType;
export declare const TbSquareXFilled: IconType;
export declare const TbSquareFilled: IconType;
export declare const TbSquaresFilled: IconType;
export declare const TbStack2Filled: IconType;
export declare const TbStack3Filled: IconType;
export declare const TbStackFilled: IconType;
export declare const TbStarHalfFilled: IconType;
export declare const TbStarFilled: IconType;
export declare const TbStarsFilled: IconType;
export declare const TbSteeringWheelFilled: IconType;
export declare const TbSunFilled: IconType;
export declare const TbSunglassesFilled: IconType;
export declare const TbSwipeDownFilled: IconType;
export declare const TbSwipeLeftFilled: IconType;
export declare const TbSwipeRightFilled: IconType;
export declare const TbSwipeUpFilled: IconType;
export declare const TbTableFilled: IconType;
export declare const TbTagFilled: IconType;
export declare const TbTagsFilled: IconType;
export declare const TbTestPipe2Filled: IconType;
export declare const TbThumbDownFilled: IconType;
export declare const TbThumbUpFilled: IconType;
export declare const TbTiltShiftFilled: IconType;
export declare const TbTimelineEventFilled: IconType;
export declare const TbToggleLeftFilled: IconType;
export declare const TbToggleRightFilled: IconType;
export declare const TbTransformFilled: IconType;
export declare const TbTransitionBottomFilled: IconType;
export declare const TbTransitionLeftFilled: IconType;
export declare const TbTransitionRightFilled: IconType;
export declare const TbTransitionTopFilled: IconType;
export declare const TbTrashXFilled: IconType;
export declare const TbTrashFilled: IconType;
export declare const TbTriangleInvertedFilled: IconType;
export declare const TbTriangleSquareCircleFilled: IconType;
export declare const TbTriangleFilled: IconType;
export declare const TbTrophyFilled: IconType;
export declare const TbUmbrellaFilled: IconType;
export declare const TbUserFilled: IconType;
export declare const TbVersionsFilled: IconType;
export declare const TbVideoFilled: IconType;
export declare const TbWindmillFilled: IconType;
export declare const TbWindsockFilled: IconType;
export declare const TbWomanFilled: IconType;
export declare const TbXboxAFilled: IconType;
export declare const TbXboxBFilled: IconType;
export declare const TbXboxXFilled: IconType;
export declare const TbXboxYFilled: IconType;
export declare const TbYinYangFilled: IconType;
export declare const TbZeppelinFilled: IconType;
export declare const TbZoomCancelFilled: IconType;
export declare const TbZoomCheckFilled: IconType;
export declare const TbZoomCodeFilled: IconType;
export declare const TbZoomExclamationFilled: IconType;
export declare const TbZoomInAreaFilled: IconType;
export declare const TbZoomInFilled: IconType;
export declare const TbZoomMoneyFilled: IconType;
export declare const TbZoomOutAreaFilled: IconType;
export declare const TbZoomOutFilled: IconType;
export declare const TbZoomPanFilled: IconType;
export declare const TbZoomQuestionFilled: IconType;
export declare const TbZoomScanFilled: IconType;
export declare const TbZoomFilled: IconType;
export declare const TbAB2: IconType;
export declare const TbABOff: IconType;
export declare const TbAB: IconType;
export declare const TbAbacusOff: IconType;
export declare const TbAbacus: IconType;
export declare const TbAbc: IconType;
export declare const TbAccessPointOff: IconType;
export declare const TbAccessPoint: IconType;
export declare const TbAccessibleOff: IconType;
export declare const TbAccessible: IconType;
export declare const TbActivityHeartbeat: IconType;
export declare const TbActivity: IconType;
export declare const TbAd2: IconType;
export declare const TbAdCircleOff: IconType;
export declare const TbAdCircle: IconType;
export declare const TbAdOff: IconType;
export declare const TbAd: IconType;
export declare const TbAddressBookOff: IconType;
export declare const TbAddressBook: IconType;
export declare const TbAdjustmentsAlt: IconType;
export declare const TbAdjustmentsBolt: IconType;
export declare const TbAdjustmentsCancel: IconType;
export declare const TbAdjustmentsCheck: IconType;
export declare const TbAdjustmentsCode: IconType;
export declare const TbAdjustmentsCog: IconType;
export declare const TbAdjustmentsDollar: IconType;
export declare const TbAdjustmentsDown: IconType;
export declare const TbAdjustmentsExclamation: IconType;
export declare const TbAdjustmentsHeart: IconType;
export declare const TbAdjustmentsHorizontal: IconType;
export declare const TbAdjustmentsMinus: IconType;
export declare const TbAdjustmentsOff: IconType;
export declare const TbAdjustmentsPause: IconType;
export declare const TbAdjustmentsPin: IconType;
export declare const TbAdjustmentsPlus: IconType;
export declare const TbAdjustmentsQuestion: IconType;
export declare const TbAdjustmentsSearch: IconType;
export declare const TbAdjustmentsShare: IconType;
export declare const TbAdjustmentsSpark: IconType;
export declare const TbAdjustmentsStar: IconType;
export declare const TbAdjustmentsUp: IconType;
export declare const TbAdjustmentsX: IconType;
export declare const TbAdjustments: IconType;
export declare const TbAerialLift: IconType;
export declare const TbAffiliate: IconType;
export declare const TbAi: IconType;
export declare const TbAirBalloon: IconType;
export declare const TbAirConditioningDisabled: IconType;
export declare const TbAirConditioning: IconType;
export declare const TbAirTrafficControl: IconType;
export declare const TbAlarmAverage: IconType;
export declare const TbAlarmMinus: IconType;
export declare const TbAlarmOff: IconType;
export declare const TbAlarmPlus: IconType;
export declare const TbAlarmSmoke: IconType;
export declare const TbAlarmSnooze: IconType;
export declare const TbAlarm: IconType;
export declare const TbAlbumOff: IconType;
export declare const TbAlbum: IconType;
export declare const TbAlertCircleOff: IconType;
export declare const TbAlertCircle: IconType;
export declare const TbAlertHexagonOff: IconType;
export declare const TbAlertHexagon: IconType;
export declare const TbAlertOctagon: IconType;
export declare const TbAlertSmallOff: IconType;
export declare const TbAlertSmall: IconType;
export declare const TbAlertSquareRoundedOff: IconType;
export declare const TbAlertSquareRounded: IconType;
export declare const TbAlertSquare: IconType;
export declare const TbAlertTriangleOff: IconType;
export declare const TbAlertTriangle: IconType;
export declare const TbAlien: IconType;
export declare const TbAlignBoxBottomCenter: IconType;
export declare const TbAlignBoxBottomLeft: IconType;
export declare const TbAlignBoxBottomRight: IconType;
export declare const TbAlignBoxCenterBottom: IconType;
export declare const TbAlignBoxCenterMiddle: IconType;
export declare const TbAlignBoxCenterStretch: IconType;
export declare const TbAlignBoxCenterTop: IconType;
export declare const TbAlignBoxLeftBottom: IconType;
export declare const TbAlignBoxLeftMiddle: IconType;
export declare const TbAlignBoxLeftStretch: IconType;
export declare const TbAlignBoxLeftTop: IconType;
export declare const TbAlignBoxRightBottom: IconType;
export declare const TbAlignBoxRightMiddle: IconType;
export declare const TbAlignBoxRightStretch: IconType;
export declare const TbAlignBoxRightTop: IconType;
export declare const TbAlignBoxTopCenter: IconType;
export declare const TbAlignBoxTopLeft: IconType;
export declare const TbAlignBoxTopRight: IconType;
export declare const TbAlignCenter: IconType;
export declare const TbAlignJustified: IconType;
export declare const TbAlignLeft2: IconType;
export declare const TbAlignLeft: IconType;
export declare const TbAlignRight2: IconType;
export declare const TbAlignRight: IconType;
export declare const TbAlpha: IconType;
export declare const TbAlphabetArabic: IconType;
export declare const TbAlphabetBangla: IconType;
export declare const TbAlphabetCyrillic: IconType;
export declare const TbAlphabetGreek: IconType;
export declare const TbAlphabetHebrew: IconType;
export declare const TbAlphabetKorean: IconType;
export declare const TbAlphabetLatin: IconType;
export declare const TbAlphabetThai: IconType;
export declare const TbAlt: IconType;
export declare const TbAmbulance: IconType;
export declare const TbAmpersand: IconType;
export declare const TbAnalyzeOff: IconType;
export declare const TbAnalyze: IconType;
export declare const TbAnchorOff: IconType;
export declare const TbAnchor: IconType;
export declare const TbAngle: IconType;
export declare const TbAnkh: IconType;
export declare const TbAntennaBars1: IconType;
export declare const TbAntennaBars2: IconType;
export declare const TbAntennaBars3: IconType;
export declare const TbAntennaBars4: IconType;
export declare const TbAntennaBars5: IconType;
export declare const TbAntennaBarsOff: IconType;
export declare const TbAntennaOff: IconType;
export declare const TbAntenna: IconType;
export declare const TbApertureOff: IconType;
export declare const TbAperture: IconType;
export declare const TbApiAppOff: IconType;
export declare const TbApiApp: IconType;
export declare const TbApiOff: IconType;
export declare const TbApi: IconType;
export declare const TbAppWindow: IconType;
export declare const TbApple: IconType;
export declare const TbAppsOff: IconType;
export declare const TbApps: IconType;
export declare const TbArcheryArrow: IconType;
export declare const TbArchiveOff: IconType;
export declare const TbArchive: IconType;
export declare const TbArmchair2Off: IconType;
export declare const TbArmchair2: IconType;
export declare const TbArmchairOff: IconType;
export declare const TbArmchair: IconType;
export declare const TbArrowAutofitContent: IconType;
export declare const TbArrowAutofitDown: IconType;
export declare const TbArrowAutofitHeight: IconType;
export declare const TbArrowAutofitLeft: IconType;
export declare const TbArrowAutofitRight: IconType;
export declare const TbArrowAutofitUp: IconType;
export declare const TbArrowAutofitWidth: IconType;
export declare const TbArrowBackUpDouble: IconType;
export declare const TbArrowBackUp: IconType;
export declare const TbArrowBack: IconType;
export declare const TbArrowBadgeDown: IconType;
export declare const TbArrowBadgeLeft: IconType;
export declare const TbArrowBadgeRight: IconType;
export declare const TbArrowBadgeUp: IconType;
export declare const TbArrowBarBoth: IconType;
export declare const TbArrowBarDown: IconType;
export declare const TbArrowBarLeft: IconType;
export declare const TbArrowBarRight: IconType;
export declare const TbArrowBarToDown: IconType;
export declare const TbArrowBarToLeft: IconType;
export declare const TbArrowBarToRight: IconType;
export declare const TbArrowBarToUp: IconType;
export declare const TbArrowBarUp: IconType;
export declare const TbArrowBearLeft2: IconType;
export declare const TbArrowBearLeft: IconType;
export declare const TbArrowBearRight2: IconType;
export declare const TbArrowBearRight: IconType;
export declare const TbArrowBigDownLine: IconType;
export declare const TbArrowBigDownLines: IconType;
export declare const TbArrowBigDown: IconType;
export declare const TbArrowBigLeftLine: IconType;
export declare const TbArrowBigLeftLines: IconType;
export declare const TbArrowBigLeft: IconType;
export declare const TbArrowBigRightLine: IconType;
export declare const TbArrowBigRightLines: IconType;
export declare const TbArrowBigRight: IconType;
export declare const TbArrowBigUpLine: IconType;
export declare const TbArrowBigUpLines: IconType;
export declare const TbArrowBigUp: IconType;
export declare const TbArrowBounce: IconType;
export declare const TbArrowCapsule: IconType;
export declare const TbArrowCurveLeft: IconType;
export declare const TbArrowCurveRight: IconType;
export declare const TbArrowDownBar: IconType;
export declare const TbArrowDownCircle: IconType;
export declare const TbArrowDownDashed: IconType;
export declare const TbArrowDownFromArc: IconType;
export declare const TbArrowDownLeftCircle: IconType;
export declare const TbArrowDownLeft: IconType;
export declare const TbArrowDownRhombus: IconType;
export declare const TbArrowDownRightCircle: IconType;
export declare const TbArrowDownRight: IconType;
export declare const TbArrowDownSquare: IconType;
export declare const TbArrowDownTail: IconType;
export declare const TbArrowDownToArc: IconType;
export declare const TbArrowDown: IconType;
export declare const TbArrowElbowLeft: IconType;
export declare const TbArrowElbowRight: IconType;
export declare const TbArrowFork: IconType;
export declare const TbArrowForwardUpDouble: IconType;
export declare const TbArrowForwardUp: IconType;
export declare const TbArrowForward: IconType;
export declare const TbArrowGuide: IconType;
export declare const TbArrowIteration: IconType;
export declare const TbArrowLeftBar: IconType;
export declare const TbArrowLeftCircle: IconType;
export declare const TbArrowLeftDashed: IconType;
export declare const TbArrowLeftFromArc: IconType;
export declare const TbArrowLeftRhombus: IconType;
export declare const TbArrowLeftRight: IconType;
export declare const TbArrowLeftSquare: IconType;
export declare const TbArrowLeftTail: IconType;
export declare const TbArrowLeftToArc: IconType;
export declare const TbArrowLeft: IconType;
export declare const TbArrowLoopLeft2: IconType;
export declare const TbArrowLoopLeft: IconType;
export declare const TbArrowLoopRight2: IconType;
export declare const TbArrowLoopRight: IconType;
export declare const TbArrowMergeAltLeft: IconType;
export declare const TbArrowMergeAltRight: IconType;
export declare const TbArrowMergeBoth: IconType;
export declare const TbArrowMergeLeft: IconType;
export declare const TbArrowMergeRight: IconType;
export declare const TbArrowMerge: IconType;
export declare const TbArrowMoveDown: IconType;
export declare const TbArrowMoveLeft: IconType;
export declare const TbArrowMoveRight: IconType;
export declare const TbArrowMoveUp: IconType;
export declare const TbArrowNarrowDownDashed: IconType;
export declare const TbArrowNarrowDown: IconType;
export declare const TbArrowNarrowLeftDashed: IconType;
export declare const TbArrowNarrowLeft: IconType;
export declare const TbArrowNarrowRightDashed: IconType;
export declare const TbArrowNarrowRight: IconType;
export declare const TbArrowNarrowUpDashed: IconType;
export declare const TbArrowNarrowUp: IconType;
export declare const TbArrowRampLeft2: IconType;
export declare const TbArrowRampLeft3: IconType;
export declare const TbArrowRampLeft: IconType;
export declare const TbArrowRampRight2: IconType;
export declare const TbArrowRampRight3: IconType;
export declare const TbArrowRampRight: IconType;
export declare const TbArrowRightBar: IconType;
export declare const TbArrowRightCircle: IconType;
export declare const TbArrowRightDashed: IconType;
export declare const TbArrowRightFromArc: IconType;
export declare const TbArrowRightRhombus: IconType;
export declare const TbArrowRightSquare: IconType;
export declare const TbArrowRightTail: IconType;
export declare const TbArrowRightToArc: IconType;
export declare const TbArrowRight: IconType;
export declare const TbArrowRotaryFirstLeft: IconType;
export declare const TbArrowRotaryFirstRight: IconType;
export declare const TbArrowRotaryLastLeft: IconType;
export declare const TbArrowRotaryLastRight: IconType;
export declare const TbArrowRotaryLeft: IconType;
export declare const TbArrowRotaryRight: IconType;
export declare const TbArrowRotaryStraight: IconType;
export declare const TbArrowRoundaboutLeft: IconType;
export declare const TbArrowRoundaboutRight: IconType;
export declare const TbArrowSharpTurnLeft: IconType;
export declare const TbArrowSharpTurnRight: IconType;
export declare const TbArrowUpBar: IconType;
export declare const TbArrowUpCircle: IconType;
export declare const TbArrowUpDashed: IconType;
export declare const TbArrowUpFromArc: IconType;
export declare const TbArrowUpLeftCircle: IconType;
export declare const TbArrowUpLeft: IconType;
export declare const TbArrowUpRhombus: IconType;
export declare const TbArrowUpRightCircle: IconType;
export declare const TbArrowUpRight: IconType;
export declare const TbArrowUpSquare: IconType;
export declare const TbArrowUpTail: IconType;
export declare const TbArrowUpToArc: IconType;
export declare const TbArrowUp: IconType;
export declare const TbArrowWaveLeftDown: IconType;
export declare const TbArrowWaveLeftUp: IconType;
export declare const TbArrowWaveRightDown: IconType;
export declare const TbArrowWaveRightUp: IconType;
export declare const TbArrowZigZag: IconType;
export declare const TbArrowsCross: IconType;
export declare const TbArrowsDiagonal2: IconType;
export declare const TbArrowsDiagonalMinimize2: IconType;
export declare const TbArrowsDiagonalMinimize: IconType;
export declare const TbArrowsDiagonal: IconType;
export declare const TbArrowsDiff: IconType;
export declare const TbArrowsDoubleNeSw: IconType;
export declare const TbArrowsDoubleNwSe: IconType;
export declare const TbArrowsDoubleSeNw: IconType;
export declare const TbArrowsDoubleSwNe: IconType;
export declare const TbArrowsDownUp: IconType;
export declare const TbArrowsDown: IconType;
export declare const TbArrowsExchange2: IconType;
export declare const TbArrowsExchange: IconType;
export declare const TbArrowsHorizontal: IconType;
export declare const TbArrowsJoin2: IconType;
export declare const TbArrowsJoin: IconType;
export declare const TbArrowsLeftDown: IconType;
export declare const TbArrowsLeftRight: IconType;
export declare const TbArrowsLeft: IconType;
export declare const TbArrowsMaximize: IconType;
export declare const TbArrowsMinimize: IconType;
export declare const TbArrowsMoveHorizontal: IconType;
export declare const TbArrowsMoveVertical: IconType;
export declare const TbArrowsMove: IconType;
export declare const TbArrowsRandom: IconType;
export declare const TbArrowsRightDown: IconType;
export declare const TbArrowsRightLeft: IconType;
export declare const TbArrowsRight: IconType;
export declare const TbArrowsShuffle2: IconType;
export declare const TbArrowsShuffle: IconType;
export declare const TbArrowsSort: IconType;
export declare const TbArrowsSplit2: IconType;
export declare const TbArrowsSplit: IconType;
export declare const TbArrowsTransferDown: IconType;
export declare const TbArrowsTransferUpDown: IconType;
export declare const TbArrowsTransferUp: IconType;
export declare const TbArrowsUpDown: IconType;
export declare const TbArrowsUpLeft: IconType;
export declare const TbArrowsUpRight: IconType;
export declare const TbArrowsUp: IconType;
export declare const TbArrowsVertical: IconType;
export declare const TbArtboardOff: IconType;
export declare const TbArtboard: IconType;
export declare const TbArticleOff: IconType;
export declare const TbArticle: IconType;
export declare const TbAspectRatioOff: IconType;
export declare const TbAspectRatio: IconType;
export declare const TbAssemblyOff: IconType;
export declare const TbAssembly: IconType;
export declare const TbAsset: IconType;
export declare const TbAsteriskSimple: IconType;
export declare const TbAsterisk: IconType;
export declare const TbAtOff: IconType;
export declare const TbAt: IconType;
export declare const TbAtom2: IconType;
export declare const TbAtomOff: IconType;
export declare const TbAtom: IconType;
export declare const TbAugmentedReality2: IconType;
export declare const TbAugmentedRealityOff: IconType;
export declare const TbAugmentedReality: IconType;
export declare const TbAuth2Fa: IconType;
export declare const TbAutomaticGearbox: IconType;
export declare const TbAutomation: IconType;
export declare const TbAvocado: IconType;
export declare const TbAwardOff: IconType;
export declare const TbAward: IconType;
export declare const TbAxe: IconType;
export declare const TbAxisX: IconType;
export declare const TbAxisY: IconType;
export declare const TbBabyBottle: IconType;
export declare const TbBabyCarriage: IconType;
export declare const TbBackground: IconType;
export declare const TbBackhoe: IconType;
export declare const TbBackpackOff: IconType;
export declare const TbBackpack: IconType;
export declare const TbBackslash: IconType;
export declare const TbBackspace: IconType;
export declare const TbBadge2K: IconType;
export declare const TbBadge3D: IconType;
export declare const TbBadge3K: IconType;
export declare const TbBadge4K: IconType;
export declare const TbBadge5K: IconType;
export declare const TbBadge8K: IconType;
export declare const TbBadgeAdOff: IconType;
export declare const TbBadgeAd: IconType;
export declare const TbBadgeAr: IconType;
export declare const TbBadgeCc: IconType;
export declare const TbBadgeHd: IconType;
export declare const TbBadgeOff: IconType;
export declare const TbBadgeSd: IconType;
export declare const TbBadgeTm: IconType;
export declare const TbBadgeVo: IconType;
export declare const TbBadgeVr: IconType;
export declare const TbBadgeWc: IconType;
export declare const TbBadge: IconType;
export declare const TbBadgesOff: IconType;
export declare const TbBadges: IconType;
export declare const TbBaguette: IconType;
export declare const TbBallAmericanFootballOff: IconType;
export declare const TbBallAmericanFootball: IconType;
export declare const TbBallBaseball: IconType;
export declare const TbBallBasketball: IconType;
export declare const TbBallBowling: IconType;
export declare const TbBallFootballOff: IconType;
export declare const TbBallFootball: IconType;
export declare const TbBallTennis: IconType;
export declare const TbBallVolleyball: IconType;
export declare const TbBalloonOff: IconType;
export declare const TbBalloon: IconType;
export declare const TbBallpenOff: IconType;
export declare const TbBallpen: IconType;
export declare const TbBan: IconType;
export declare const TbBandageOff: IconType;
export declare const TbBandage: IconType;
export declare const TbBarbellOff: IconType;
export declare const TbBarbell: IconType;
export declare const TbBarcodeOff: IconType;
export declare const TbBarcode: IconType;
export declare const TbBarrelOff: IconType;
export declare const TbBarrel: IconType;
export declare const TbBarrierBlockOff: IconType;
export declare const TbBarrierBlock: IconType;
export declare const TbBaselineDensityLarge: IconType;
export declare const TbBaselineDensityMedium: IconType;
export declare const TbBaselineDensitySmall: IconType;
export declare const TbBaseline: IconType;
export declare const TbBasketBolt: IconType;
export declare const TbBasketCancel: IconType;
export declare const TbBasketCheck: IconType;
export declare const TbBasketCode: IconType;
export declare const TbBasketCog: IconType;
export declare const TbBasketDiscount: IconType;
export declare const TbBasketDollar: IconType;
export declare const TbBasketDown: IconType;
export declare const TbBasketExclamation: IconType;
export declare const TbBasketHeart: IconType;
export declare const TbBasketMinus: IconType;
export declare const TbBasketOff: IconType;
export declare const TbBasketPause: IconType;
export declare const TbBasketPin: IconType;
export declare const TbBasketPlus: IconType;
export declare const TbBasketQuestion: IconType;
export declare const TbBasketSearch: IconType;
export declare const TbBasketShare: IconType;
export declare const TbBasketStar: IconType;
export declare const TbBasketUp: IconType;
export declare const TbBasketX: IconType;
export declare const TbBasket: IconType;
export declare const TbBat: IconType;
export declare const TbBathOff: IconType;
export declare const TbBath: IconType;
export declare const TbBattery1: IconType;
export declare const TbBattery2: IconType;
export declare const TbBattery3: IconType;
export declare const TbBattery4: IconType;
export declare const TbBatteryAutomotive: IconType;
export declare const TbBatteryCharging2: IconType;
export declare const TbBatteryCharging: IconType;
export declare const TbBatteryEco: IconType;
export declare const TbBatteryExclamation: IconType;
export declare const TbBatteryOff: IconType;
export declare const TbBatterySpark: IconType;
export declare const TbBatteryVertical1: IconType;
export declare const TbBatteryVertical2: IconType;
export declare const TbBatteryVertical3: IconType;
export declare const TbBatteryVertical4: IconType;
export declare const TbBatteryVerticalCharging2: IconType;
export declare const TbBatteryVerticalCharging: IconType;
export declare const TbBatteryVerticalEco: IconType;
export declare const TbBatteryVerticalExclamation: IconType;
export declare const TbBatteryVerticalOff: IconType;
export declare const TbBatteryVertical: IconType;
export declare const TbBattery: IconType;
export declare const TbBeachOff: IconType;
export declare const TbBeach: IconType;
export declare const TbBedFlat: IconType;
export declare const TbBedOff: IconType;
export declare const TbBed: IconType;
export declare const TbBeerOff: IconType;
export declare const TbBeer: IconType;
export declare const TbBellBolt: IconType;
export declare const TbBellCancel: IconType;
export declare const TbBellCheck: IconType;
export declare const TbBellCode: IconType;
export declare const TbBellCog: IconType;
export declare const TbBellDollar: IconType;
export declare const TbBellDown: IconType;
export declare const TbBellExclamation: IconType;
export declare const TbBellHeart: IconType;
export declare const TbBellMinus: IconType;
export declare const TbBellOff: IconType;
export declare const TbBellPause: IconType;
export declare const TbBellPin: IconType;
export declare const TbBellPlus: IconType;
export declare const TbBellQuestion: IconType;
export declare const TbBellRinging2: IconType;
export declare const TbBellRinging: IconType;
export declare const TbBellSchool: IconType;
export declare const TbBellSearch: IconType;
export declare const TbBellShare: IconType;
export declare const TbBellStar: IconType;
export declare const TbBellUp: IconType;
export declare const TbBellX: IconType;
export declare const TbBellZ: IconType;
export declare const TbBell: IconType;
export declare const TbBeta: IconType;
export declare const TbBible: IconType;
export declare const TbBikeOff: IconType;
export declare const TbBike: IconType;
export declare const TbBinaryOff: IconType;
export declare const TbBinaryTree2: IconType;
export declare const TbBinaryTree: IconType;
export declare const TbBinary: IconType;
export declare const TbBinoculars: IconType;
export declare const TbBiohazardOff: IconType;
export declare const TbBiohazard: IconType;
export declare const TbBlade: IconType;
export declare const TbBleachChlorine: IconType;
export declare const TbBleachNoChlorine: IconType;
export declare const TbBleachOff: IconType;
export declare const TbBleach: IconType;
export declare const TbBlendMode: IconType;
export declare const TbBlender: IconType;
export declare const TbBlob: IconType;
export declare const TbBlockquote: IconType;
export declare const TbBlocks: IconType;
export declare const TbBluetoothConnected: IconType;
export declare const TbBluetoothOff: IconType;
export declare const TbBluetoothX: IconType;
export declare const TbBluetooth: IconType;
export declare const TbBlurOff: IconType;
export declare const TbBlur: IconType;
export declare const TbBmp: IconType;
export declare const TbBodyScan: IconType;
export declare const TbBoldOff: IconType;
export declare const TbBold: IconType;
export declare const TbBoltOff: IconType;
export declare const TbBolt: IconType;
export declare const TbBomb: IconType;
export declare const TbBoneOff: IconType;
export declare const TbBone: IconType;
export declare const TbBongOff: IconType;
export declare const TbBong: IconType;
export declare const TbBook2: IconType;
export declare const TbBookDownload: IconType;
export declare const TbBookOff: IconType;
export declare const TbBookUpload: IconType;
export declare const TbBook: IconType;
export declare const TbBookmarkAi: IconType;
export declare const TbBookmarkEdit: IconType;
export declare const TbBookmarkMinus: IconType;
export declare const TbBookmarkOff: IconType;
export declare const TbBookmarkPlus: IconType;
export declare const TbBookmarkQuestion: IconType;
export declare const TbBookmark: IconType;
export declare const TbBookmarksOff: IconType;
export declare const TbBookmarks: IconType;
export declare const TbBooksOff: IconType;
export declare const TbBooks: IconType;
export declare const TbBoom: IconType;
export declare const TbBorderAll: IconType;
export declare const TbBorderBottomPlus: IconType;
export declare const TbBorderBottom: IconType;
export declare const TbBorderCornerIos: IconType;
export declare const TbBorderCornerPill: IconType;
export declare const TbBorderCornerRounded: IconType;
export declare const TbBorderCornerSquare: IconType;
export declare const TbBorderCorners: IconType;
export declare const TbBorderHorizontal: IconType;
export declare const TbBorderInner: IconType;
export declare const TbBorderLeftPlus: IconType;
export declare const TbBorderLeft: IconType;
export declare const TbBorderNone: IconType;
export declare const TbBorderOuter: IconType;
export declare const TbBorderRadius: IconType;
export declare const TbBorderRightPlus: IconType;
export declare const TbBorderRight: IconType;
export declare const TbBorderSides: IconType;
export declare const TbBorderStyle2: IconType;
export declare const TbBorderStyle: IconType;
export declare const TbBorderTopPlus: IconType;
export declare const TbBorderTop: IconType;
export declare const TbBorderVertical: IconType;
export declare const TbBottleOff: IconType;
export declare const TbBottle: IconType;
export declare const TbBounceLeft: IconType;
export declare const TbBounceRight: IconType;
export declare const TbBow: IconType;
export declare const TbBowlChopsticks: IconType;
export declare const TbBowlSpoon: IconType;
export declare const TbBowl: IconType;
export declare const TbBowling: IconType;
export declare const TbBoxAlignBottomLeft: IconType;
export declare const TbBoxAlignBottomRight: IconType;
export declare const TbBoxAlignBottom: IconType;
export declare const TbBoxAlignLeft: IconType;
export declare const TbBoxAlignRight: IconType;
export declare const TbBoxAlignTopLeft: IconType;
export declare const TbBoxAlignTopRight: IconType;
export declare const TbBoxAlignTop: IconType;
export declare const TbBoxMargin: IconType;
export declare const TbBoxModel2Off: IconType;
export declare const TbBoxModel2: IconType;
export declare const TbBoxModelOff: IconType;
export declare const TbBoxModel: IconType;
export declare const TbBoxMultiple0: IconType;
export declare const TbBoxMultiple1: IconType;
export declare const TbBoxMultiple2: IconType;
export declare const TbBoxMultiple3: IconType;
export declare const TbBoxMultiple4: IconType;
export declare const TbBoxMultiple5: IconType;
export declare const TbBoxMultiple6: IconType;
export declare const TbBoxMultiple7: IconType;
export declare const TbBoxMultiple8: IconType;
export declare const TbBoxMultiple9: IconType;
export declare const TbBoxMultiple: IconType;
export declare const TbBoxOff: IconType;
export declare const TbBoxPadding: IconType;
export declare const TbBox: IconType;
export declare const TbBracesOff: IconType;
export declare const TbBraces: IconType;
export declare const TbBracketsAngleOff: IconType;
export declare const TbBracketsAngle: IconType;
export declare const TbBracketsContainEnd: IconType;
export declare const TbBracketsContainStart: IconType;
export declare const TbBracketsContain: IconType;
export declare const TbBracketsOff: IconType;
export declare const TbBrackets: IconType;
export declare const TbBraille: IconType;
export declare const TbBrain: IconType;
export declare const TbBrand4Chan: IconType;
export declare const TbBrandAbstract: IconType;
export declare const TbBrandAdobeAfterEffect: IconType;
export declare const TbBrandAdobeIllustrator: IconType;
export declare const TbBrandAdobeIndesign: IconType;
export declare const TbBrandAdobePhotoshop: IconType;
export declare const TbBrandAdobePremier: IconType;
export declare const TbBrandAdobeXd: IconType;
export declare const TbBrandAdobe: IconType;
export declare const TbBrandAdonisJs: IconType;
export declare const TbBrandAirbnb: IconType;
export declare const TbBrandAirtable: IconType;
export declare const TbBrandAlgolia: IconType;
export declare const TbBrandAlipay: IconType;
export declare const TbBrandAlpineJs: IconType;
export declare const TbBrandAmazon: IconType;
export declare const TbBrandAmd: IconType;
export declare const TbBrandAmie: IconType;
export declare const TbBrandAmigo: IconType;
export declare const TbBrandAmongUs: IconType;
export declare const TbBrandAndroid: IconType;
export declare const TbBrandAngular: IconType;
export declare const TbBrandAnsible: IconType;
export declare const TbBrandAo3: IconType;
export declare const TbBrandAppgallery: IconType;
export declare const TbBrandAppleArcade: IconType;
export declare const TbBrandAppleNews: IconType;
export declare const TbBrandApplePodcast: IconType;
export declare const TbBrandApple: IconType;
export declare const TbBrandAppstore: IconType;
export declare const TbBrandArc: IconType;
export declare const TbBrandAsana: IconType;
export declare const TbBrandAstro: IconType;
export declare const TbBrandAuth0: IconType;
export declare const TbBrandAws: IconType;
export declare const TbBrandAzure: IconType;
export declare const TbBrandBackbone: IconType;
export declare const TbBrandBadoo: IconType;
export declare const TbBrandBaidu: IconType;
export declare const TbBrandBandcamp: IconType;
export declare const TbBrandBandlab: IconType;
export declare const TbBrandBeats: IconType;
export declare const TbBrandBebo: IconType;
export declare const TbBrandBehance: IconType;
export declare const TbBrandBilibili: IconType;
export declare const TbBrandBinance: IconType;
export declare const TbBrandBing: IconType;
export declare const TbBrandBitbucket: IconType;
export declare const TbBrandBlackberry: IconType;
export declare const TbBrandBlender: IconType;
export declare const TbBrandBlogger: IconType;
export declare const TbBrandBluesky: IconType;
export declare const TbBrandBooking: IconType;
export declare const TbBrandBootstrap: IconType;
export declare const TbBrandBulma: IconType;
export declare const TbBrandBumble: IconType;
export declare const TbBrandBunpo: IconType;
export declare const TbBrandCSharp: IconType;
export declare const TbBrandCake: IconType;
export declare const TbBrandCakephp: IconType;
export declare const TbBrandCampaignmonitor: IconType;
export declare const TbBrandCarbon: IconType;
export declare const TbBrandCashapp: IconType;
export declare const TbBrandChrome: IconType;
export declare const TbBrandCinema4D: IconType;
export declare const TbBrandCitymapper: IconType;
export declare const TbBrandCloudflare: IconType;
export declare const TbBrandCodecov: IconType;
export declare const TbBrandCodepen: IconType;
export declare const TbBrandCodesandbox: IconType;
export declare const TbBrandCohost: IconType;
export declare const TbBrandCoinbase: IconType;
export declare const TbBrandComedyCentral: IconType;
export declare const TbBrandCoreos: IconType;
export declare const TbBrandCouchdb: IconType;
export declare const TbBrandCouchsurfing: IconType;
export declare const TbBrandCpp: IconType;
export declare const TbBrandCraft: IconType;
export declare const TbBrandCrunchbase: IconType;
export declare const TbBrandCss3: IconType;
export declare const TbBrandCtemplar: IconType;
export declare const TbBrandCucumber: IconType;
export declare const TbBrandCupra: IconType;
export declare const TbBrandCypress: IconType;
export declare const TbBrandD3: IconType;
export declare const TbBrandDatabricks: IconType;
export declare const TbBrandDaysCounter: IconType;
export declare const TbBrandDcos: IconType;
export declare const TbBrandDebian: IconType;
export declare const TbBrandDeezer: IconType;
export declare const TbBrandDeliveroo: IconType;
export declare const TbBrandDeno: IconType;
export declare const TbBrandDenodo: IconType;
export declare const TbBrandDeviantart: IconType;
export declare const TbBrandDigg: IconType;
export declare const TbBrandDingtalk: IconType;
export declare const TbBrandDiscord: IconType;
export declare const TbBrandDisney: IconType;
export declare const TbBrandDisqus: IconType;
export declare const TbBrandDjango: IconType;
export declare const TbBrandDocker: IconType;
export declare const TbBrandDoctrine: IconType;
export declare const TbBrandDolbyDigital: IconType;
export declare const TbBrandDouban: IconType;
export declare const TbBrandDribbble: IconType;
export declare const TbBrandDrops: IconType;
export declare const TbBrandDrupal: IconType;
export declare const TbBrandEdge: IconType;
export declare const TbBrandElastic: IconType;
export declare const TbBrandElectronicArts: IconType;
export declare const TbBrandEmber: IconType;
export declare const TbBrandEnvato: IconType;
export declare const TbBrandEtsy: IconType;
export declare const TbBrandEvernote: IconType;
export declare const TbBrandFacebook: IconType;
export declare const TbBrandFeedly: IconType;
export declare const TbBrandFigma: IconType;
export declare const TbBrandFilezilla: IconType;
export declare const TbBrandFinder: IconType;
export declare const TbBrandFirebase: IconType;
export declare const TbBrandFirefox: IconType;
export declare const TbBrandFiverr: IconType;
export declare const TbBrandFlickr: IconType;
export declare const TbBrandFlightradar24: IconType;
export declare const TbBrandFlipboard: IconType;
export declare const TbBrandFlutter: IconType;
export declare const TbBrandFortnite: IconType;
export declare const TbBrandFoursquare: IconType;
export declare const TbBrandFramerMotion: IconType;
export declare const TbBrandFramer: IconType;
export declare const TbBrandFunimation: IconType;
export declare const TbBrandGatsby: IconType;
export declare const TbBrandGit: IconType;
export declare const TbBrandGithubCopilot: IconType;
export declare const TbBrandGithub: IconType;
export declare const TbBrandGitlab: IconType;
export declare const TbBrandGmail: IconType;
export declare const TbBrandGolang: IconType;
export declare const TbBrandGoogleAnalytics: IconType;
export declare const TbBrandGoogleBigQuery: IconType;
export declare const TbBrandGoogleDrive: IconType;
export declare const TbBrandGoogleFit: IconType;
export declare const TbBrandGoogleHome: IconType;
export declare const TbBrandGoogleMaps: IconType;
export declare const TbBrandGoogleOne: IconType;
export declare const TbBrandGooglePhotos: IconType;
export declare const TbBrandGooglePlay: IconType;
export declare const TbBrandGooglePodcasts: IconType;
export declare const TbBrandGoogle: IconType;
export declare const TbBrandGrammarly: IconType;
export declare const TbBrandGraphql: IconType;
export declare const TbBrandGravatar: IconType;
export declare const TbBrandGrindr: IconType;
export declare const TbBrandGuardian: IconType;
export declare const TbBrandGumroad: IconType;
export declare const TbBrandHackerrank: IconType;
export declare const TbBrandHbo: IconType;
export declare const TbBrandHeadlessui: IconType;
export declare const TbBrandHexo: IconType;
export declare const TbBrandHipchat: IconType;
export declare const TbBrandHtml5: IconType;
export declare const TbBrandInertia: IconType;
export declare const TbBrandInstagram: IconType;
export declare const TbBrandIntercom: IconType;
export declare const TbBrandItch: IconType;
export declare const TbBrandJavascript: IconType;
export declare const TbBrandJuejin: IconType;
export declare const TbBrandKakoTalk: IconType;
export declare const TbBrandKbin: IconType;
export declare const TbBrandKick: IconType;
export declare const TbBrandKickstarter: IconType;
export declare const TbBrandKotlin: IconType;
export declare const TbBrandLaravel: IconType;
export declare const TbBrandLastfm: IconType;
export declare const TbBrandLeetcode: IconType;
export declare const TbBrandLetterboxd: IconType;
export declare const TbBrandLine: IconType;
export declare const TbBrandLinkedin: IconType;
export declare const TbBrandLinktree: IconType;
export declare const TbBrandLinqpad: IconType;
export declare const TbBrandLivewire: IconType;
export declare const TbBrandLoom: IconType;
export declare const TbBrandMailgun: IconType;
export declare const TbBrandMantine: IconType;
export declare const TbBrandMastercard: IconType;
export declare const TbBrandMastodon: IconType;
export declare const TbBrandMatrix: IconType;
export declare const TbBrandMcdonalds: IconType;
export declare const TbBrandMedium: IconType;
export declare const TbBrandMeetup: IconType;
export declare const TbBrandMercedes: IconType;
export declare const TbBrandMessenger: IconType;
export declare const TbBrandMeta: IconType;
export declare const TbBrandMetabrainz: IconType;
export declare const TbBrandMinecraft: IconType;
export declare const TbBrandMiniprogram: IconType;
export declare const TbBrandMixpanel: IconType;
export declare const TbBrandMonday: IconType;
export declare const TbBrandMongodb: IconType;
export declare const TbBrandMyOppo: IconType;
export declare const TbBrandMysql: IconType;
export declare const TbBrandNationalGeographic: IconType;
export declare const TbBrandNem: IconType;
export declare const TbBrandNetbeans: IconType;
export declare const TbBrandNeteaseMusic: IconType;
export declare const TbBrandNetflix: IconType;
export declare const TbBrandNexo: IconType;
export declare const TbBrandNextcloud: IconType;
export declare const TbBrandNextjs: IconType;
export declare const TbBrandNodejs: IconType;
export declare const TbBrandNordVpn: IconType;
export declare const TbBrandNotion: IconType;
export declare const TbBrandNpm: IconType;
export declare const TbBrandNuxt: IconType;
export declare const TbBrandNytimes: IconType;
export declare const TbBrandOauth: IconType;
export declare const TbBrandOffice: IconType;
export declare const TbBrandOkRu: IconType;
export declare const TbBrandOnedrive: IconType;
export declare const TbBrandOnlyfans: IconType;
export declare const TbBrandOpenSource: IconType;
export declare const TbBrandOpenai: IconType;
export declare const TbBrandOpenvpn: IconType;
export declare const TbBrandOpera: IconType;
export declare const TbBrandPagekit: IconType;
export declare const TbBrandParsinta: IconType;
export declare const TbBrandPatreon: IconType;
export declare const TbBrandPaypal: IconType;
export declare const TbBrandPaypay: IconType;
export declare const TbBrandPeanut: IconType;
export declare const TbBrandPepsi: IconType;
export declare const TbBrandPhp: IconType;
export declare const TbBrandPicsart: IconType;
export declare const TbBrandPinterest: IconType;
export declare const TbBrandPlanetscale: IconType;
export declare const TbBrandPnpm: IconType;
export declare const TbBrandPocket: IconType;
export declare const TbBrandPolymer: IconType;
export declare const TbBrandPowershell: IconType;
export declare const TbBrandPrintables: IconType;
export declare const TbBrandPrisma: IconType;
export declare const TbBrandProducthunt: IconType;
export declare const TbBrandPushbullet: IconType;
export declare const TbBrandPushover: IconType;
export declare const TbBrandPython: IconType;
export declare const TbBrandQq: IconType;
export declare const TbBrandRadixUi: IconType;
export declare const TbBrandReactNative: IconType;
export declare const TbBrandReact: IconType;
export declare const TbBrandReason: IconType;
export declare const TbBrandReddit: IconType;
export declare const TbBrandRedhat: IconType;
export declare const TbBrandRedux: IconType;
export declare const TbBrandRevolut: IconType;
export declare const TbBrandRumble: IconType;
export declare const TbBrandRust: IconType;
export declare const TbBrandSafari: IconType;
export declare const TbBrandSamsungpass: IconType;
export declare const TbBrandSass: IconType;
export declare const TbBrandSentry: IconType;
export declare const TbBrandSharik: IconType;
export declare const TbBrandShazam: IconType;
export declare const TbBrandShopee: IconType;
export declare const TbBrandSketch: IconType;
export declare const TbBrandSkype: IconType;
export declare const TbBrandSlack: IconType;
export declare const TbBrandSnapchat: IconType;
export declare const TbBrandSnapseed: IconType;
export declare const TbBrandSnowflake: IconType;
export declare const TbBrandSocketIo: IconType;
export declare const TbBrandSolidjs: IconType;
export declare const TbBrandSoundcloud: IconType;
export declare const TbBrandSpacehey: IconType;
export declare const TbBrandSpeedtest: IconType;
export declare const TbBrandSpotify: IconType;
export declare const TbBrandStackoverflow: IconType;
export declare const TbBrandStackshare: IconType;
export declare const TbBrandSteam: IconType;
export declare const TbBrandStocktwits: IconType;
export declare const TbBrandStorj: IconType;
export declare const TbBrandStorybook: IconType;
export declare const TbBrandStorytel: IconType;
export declare const TbBrandStrava: IconType;
export declare const TbBrandStripe: IconType;
export declare const TbBrandSublimeText: IconType;
export declare const TbBrandSugarizer: IconType;
export declare const TbBrandSupabase: IconType;
export declare const TbBrandSuperhuman: IconType;
export declare const TbBrandSupernova: IconType;
export declare const TbBrandSurfshark: IconType;
export declare const TbBrandSvelte: IconType;
export declare const TbBrandSwift: IconType;
export declare const TbBrandSymfony: IconType;
export declare const TbBrandTabler: IconType;
export declare const TbBrandTailwind: IconType;
export declare const TbBrandTaobao: IconType;
export declare const TbBrandTeams: IconType;
export declare const TbBrandTed: IconType;
export declare const TbBrandTelegram: IconType;
export declare const TbBrandTerraform: IconType;
export declare const TbBrandTesla: IconType;
export declare const TbBrandTether: IconType;
export declare const TbBrandThingiverse: IconType;
export declare const TbBrandThreads: IconType;
export declare const TbBrandThreejs: IconType;
export declare const TbBrandTidal: IconType;
export declare const TbBrandTiktok: IconType;
export declare const TbBrandTinder: IconType;
export declare const TbBrandTopbuzz: IconType;
export declare const TbBrandTorchain: IconType;
export declare const TbBrandToyota: IconType;
export declare const TbBrandTrello: IconType;
export declare const TbBrandTripadvisor: IconType;
export declare const TbBrandTumblr: IconType;
export declare const TbBrandTwilio: IconType;
export declare const TbBrandTwitch: IconType;
export declare const TbBrandTwitter: IconType;
export declare const TbBrandTypescript: IconType;
export declare const TbBrandUber: IconType;
export declare const TbBrandUbuntu: IconType;
export declare const TbBrandUnity: IconType;
export declare const TbBrandUnsplash: IconType;
export declare const TbBrandUpwork: IconType;
export declare const TbBrandValorant: IconType;
export declare const TbBrandVercel: IconType;
export declare const TbBrandVimeo: IconType;
export declare const TbBrandVinted: IconType;
export declare const TbBrandVisa: IconType;
export declare const TbBrandVisualStudio: IconType;
export declare const TbBrandVite: IconType;
export declare const TbBrandVivaldi: IconType;
export declare const TbBrandVk: IconType;
export declare const TbBrandVlc: IconType;
export declare const TbBrandVolkswagen: IconType;
export declare const TbBrandVsco: IconType;
export declare const TbBrandVscode: IconType;
export declare const TbBrandVue: IconType;
export declare const TbBrandWalmart: IconType;
export declare const TbBrandWaze: IconType;
export declare const TbBrandWebflow: IconType;
export declare const TbBrandWechat: IconType;
export declare const TbBrandWeibo: IconType;
export declare const TbBrandWhatsapp: IconType;
export declare const TbBrandWikipedia: IconType;
export declare const TbBrandWindows: IconType;
export declare const TbBrandWindy: IconType;
export declare const TbBrandWish: IconType;
export declare const TbBrandWix: IconType;
export declare const TbBrandWordpress: IconType;
export declare const TbBrandX: IconType;
export declare const TbBrandXamarin: IconType;
export declare const TbBrandXbox: IconType;
export declare const TbBrandXdeep: IconType;
export declare const TbBrandXing: IconType;
export declare const TbBrandYahoo: IconType;
export declare const TbBrandYandex: IconType;
export declare const TbBrandYarn: IconType;
export declare const TbBrandYatse: IconType;
export declare const TbBrandYcombinator: IconType;
export declare const TbBrandYoutubeKids: IconType;
export declare const TbBrandYoutube: IconType;
export declare const TbBrandZalando: IconType;
export declare const TbBrandZapier: IconType;
export declare const TbBrandZeit: IconType;
export declare const TbBrandZhihu: IconType;
export declare const TbBrandZoom: IconType;
export declare const TbBrandZulip: IconType;
export declare const TbBrandZwift: IconType;
export declare const TbBreadOff: IconType;
export declare const TbBread: IconType;
export declare const TbBriefcase2: IconType;
export declare const TbBriefcaseOff: IconType;
export declare const TbBriefcase: IconType;
export declare const TbBrightness2: IconType;
export declare const TbBrightnessAuto: IconType;
export declare const TbBrightnessDown: IconType;
export declare const TbBrightnessHalf: IconType;
export declare const TbBrightnessOff: IconType;
export declare const TbBrightnessUp: IconType;
export declare const TbBrightness: IconType;
export declare const TbBroadcastOff: IconType;
export declare const TbBroadcast: IconType;
export declare const TbBrowserCheck: IconType;
export declare const TbBrowserMaximize: IconType;
export declare const TbBrowserMinus: IconType;
export declare const TbBrowserOff: IconType;
export declare const TbBrowserPlus: IconType;
export declare const TbBrowserShare: IconType;
export declare const TbBrowserX: IconType;
export declare const TbBrowser: IconType;
export declare const TbBrushOff: IconType;
export declare const TbBrush: IconType;
export declare const TbBubbleMinus: IconType;
export declare const TbBubblePlus: IconType;
export declare const TbBubbleTea2: IconType;
export declare const TbBubbleTea: IconType;
export declare const TbBubbleText: IconType;
export declare const TbBubbleX: IconType;
export declare const TbBubble: IconType;
export declare const TbBucketDroplet: IconType;
export declare const TbBucketOff: IconType;
export declare const TbBucket: IconType;
export declare const TbBugOff: IconType;
export declare const TbBug: IconType;
export declare const TbBuildingAirport: IconType;
export declare const TbBuildingArch: IconType;
export declare const TbBuildingBank: IconType;
export declare const TbBuildingBridge2: IconType;
export declare const TbBuildingBridge: IconType;
export declare const TbBuildingBroadcastTower: IconType;
export declare const TbBuildingBurjAlArab: IconType;
export declare const TbBuildingCarousel: IconType;
export declare const TbBuildingCastle: IconType;
export declare const TbBuildingChurch: IconType;
export declare const TbBuildingCircus: IconType;
export declare const TbBuildingCog: IconType;
export declare const TbBuildingCommunity: IconType;
export declare const TbBuildingCottage: IconType;
export declare const TbBuildingEstate: IconType;
export declare const TbBuildingFactory2: IconType;
export declare const TbBuildingFactory: IconType;
export declare const TbBuildingFortress: IconType;
export declare const TbBuildingHospital: IconType;
export declare const TbBuildingLighthouse: IconType;
export declare const TbBuildingMinus: IconType;
export declare const TbBuildingMonument: IconType;
export declare const TbBuildingMosque: IconType;
export declare const TbBuildingOff: IconType;
export declare const TbBuildingPavilion: IconType;
export declare const TbBuildingPlus: IconType;
export declare const TbBuildingSkyscraper: IconType;
export declare const TbBuildingStadium: IconType;
export declare const TbBuildingStore: IconType;
export declare const TbBuildingTunnel: IconType;
export declare const TbBuildingWarehouse: IconType;
export declare const TbBuildingWindTurbine: IconType;
export declare const TbBuilding: IconType;
export declare const TbBuildings: IconType;
export declare const TbBulbOff: IconType;
export declare const TbBulb: IconType;
export declare const TbBulldozer: IconType;
export declare const TbBurger: IconType;
export declare const TbBusOff: IconType;
export declare const TbBusStop: IconType;
export declare const TbBus: IconType;
export declare const TbBusinessplan: IconType;
export declare const TbButterfly: IconType;
export declare const TbCactusOff: IconType;
export declare const TbCactus: IconType;
export declare const TbCakeOff: IconType;
export declare const TbCakeRoll: IconType;
export declare const TbCake: IconType;
export declare const TbCalculatorOff: IconType;
export declare const TbCalculator: IconType;
export declare const TbCalendarBolt: IconType;
export declare const TbCalendarCancel: IconType;
export declare const TbCalendarCheck: IconType;
export declare const TbCalendarClock: IconType;
export declare const TbCalendarCode: IconType;
export declare const TbCalendarCog: IconType;
export declare const TbCalendarDollar: IconType;
export declare const TbCalendarDot: IconType;
export declare const TbCalendarDown: IconType;
export declare const TbCalendarDue: IconType;
export declare const TbCalendarEvent: IconType;
export declare const TbCalendarExclamation: IconType;
export declare const TbCalendarHeart: IconType;
export declare const TbCalendarMinus: IconType;
export declare const TbCalendarMonth: IconType;
export declare const TbCalendarOff: IconType;
export declare const TbCalendarPause: IconType;
export declare const TbCalendarPin: IconType;
export declare const TbCalendarPlus: IconType;
export declare const TbCalendarQuestion: IconType;
export declare const TbCalendarRepeat: IconType;
export declare const TbCalendarSad: IconType;
export declare const TbCalendarSearch: IconType;
export declare const TbCalendarShare: IconType;
export declare const TbCalendarSmile: IconType;
export declare const TbCalendarStar: IconType;
export declare const TbCalendarStats: IconType;
export declare const TbCalendarTime: IconType;
export declare const TbCalendarUp: IconType;
export declare const TbCalendarUser: IconType;
export declare const TbCalendarWeek: IconType;
export declare const TbCalendarX: IconType;
export declare const TbCalendar: IconType;
export declare const TbCameraAi: IconType;
export declare const TbCameraBitcoin: IconType;
export declare const TbCameraBolt: IconType;
export declare const TbCameraCancel: IconType;
export declare const TbCameraCheck: IconType;
export declare const TbCameraCode: IconType;
export declare const TbCameraCog: IconType;
export declare const TbCameraDollar: IconType;
export declare const TbCameraDown: IconType;
export declare const TbCameraExclamation: IconType;
export declare const TbCameraHeart: IconType;
export declare const TbCameraMinus: IconType;
export declare const TbCameraMoon: IconType;
export declare const TbCameraOff: IconType;
export declare const TbCameraPause: IconType;
export declare const TbCameraPin: IconType;
export declare const TbCameraPlus: IconType;
export declare const TbCameraQuestion: IconType;
export declare const TbCameraRotate: IconType;
export declare const TbCameraSearch: IconType;
export declare const TbCameraSelfie: IconType;
export declare const TbCameraShare: IconType;
export declare const TbCameraSpark: IconType;
export declare const TbCameraStar: IconType;
export declare const TbCameraUp: IconType;
export declare const TbCameraX: IconType;
export declare const TbCamera: IconType;
export declare const TbCamper: IconType;
export declare const TbCampfire: IconType;
export declare const TbCancel: IconType;
export declare const TbCandle: IconType;
export declare const TbCandyOff: IconType;
export declare const TbCandy: IconType;
export declare const TbCane: IconType;
export declare const TbCannabis: IconType;
export declare const TbCapProjecting: IconType;
export declare const TbCapRounded: IconType;
export declare const TbCapStraight: IconType;
export declare const TbCapsuleHorizontal: IconType;
export declare const TbCapsule: IconType;
export declare const TbCaptureOff: IconType;
export declare const TbCapture: IconType;
export declare const TbCar4Wd: IconType;
export declare const TbCarCrane: IconType;
export declare const TbCarCrash: IconType;
export declare const TbCarFan1: IconType;
export declare const TbCarFan2: IconType;
export declare const TbCarFan3: IconType;
export declare const TbCarFanAuto: IconType;
export declare const TbCarFan: IconType;
export declare const TbCarGarage: IconType;
export declare const TbCarOff: IconType;
export declare const TbCarSuv: IconType;
export declare const TbCarTurbine: IconType;
export declare const TbCar: IconType;
export declare const TbCarambola: IconType;
export declare const TbCaravan: IconType;
export declare const TbCardboardsOff: IconType;
export declare const TbCardboards: IconType;
export declare const TbCards: IconType;
export declare const TbCaretDown: IconType;
export declare const TbCaretLeftRight: IconType;
export declare const TbCaretLeft: IconType;
export declare const TbCaretRight: IconType;
export declare const TbCaretUpDown: IconType;
export declare const TbCaretUp: IconType;
export declare const TbCarouselHorizontal: IconType;
export declare const TbCarouselVertical: IconType;
export declare const TbCarrotOff: IconType;
export declare const TbCarrot: IconType;
export declare const TbCashBanknoteOff: IconType;
export declare const TbCashBanknote: IconType;
export declare const TbCashOff: IconType;
export declare const TbCashRegister: IconType;
export declare const TbCash: IconType;
export declare const TbCastOff: IconType;
export declare const TbCast: IconType;
export declare const TbCat: IconType;
export declare const TbCategory2: IconType;
export declare const TbCategoryMinus: IconType;
export declare const TbCategoryPlus: IconType;
export declare const TbCategory: IconType;
export declare const TbCeOff: IconType;
export declare const TbCe: IconType;
export declare const TbCellSignal1: IconType;
export declare const TbCellSignal2: IconType;
export declare const TbCellSignal3: IconType;
export declare const TbCellSignal4: IconType;
export declare const TbCellSignal5: IconType;
export declare const TbCellSignalOff: IconType;
export declare const TbCell: IconType;
export declare const TbCertificate2Off: IconType;
export declare const TbCertificate2: IconType;
export declare const TbCertificateOff: IconType;
export declare const TbCertificate: IconType;
export declare const TbChairDirector: IconType;
export declare const TbChalkboardOff: IconType;
export declare const TbChalkboard: IconType;
export declare const TbChargingPile: IconType;
export declare const TbChartArcs3: IconType;
export declare const TbChartArcs: IconType;
export declare const TbChartAreaLine: IconType;
export declare const TbChartArea: IconType;
export declare const TbChartArrowsVertical: IconType;
export declare const TbChartArrows: IconType;
export declare const TbChartBarOff: IconType;
export declare const TbChartBarPopular: IconType;
export declare const TbChartBar: IconType;
export declare const TbChartBubble: IconType;
export declare const TbChartCandle: IconType;
export declare const TbChartCircles: IconType;
export declare const TbChartCohort: IconType;
export declare const TbChartColumn: IconType;
export declare const TbChartCovariate: IconType;
export declare const TbChartDonut2: IconType;
export declare const TbChartDonut3: IconType;
export declare const TbChartDonut4: IconType;
export declare const TbChartDonut: IconType;
export declare const TbChartDots2: IconType;
export declare const TbChartDots3: IconType;
export declare const TbChartDots: IconType;
export declare const TbChartFunnel: IconType;
export declare const TbChartGridDots: IconType;
export declare const TbChartHistogram: IconType;
export declare const TbChartInfographic: IconType;
export declare const TbChartLine: IconType;
export declare const TbChartPie2: IconType;
export declare const TbChartPie3: IconType;
export declare const TbChartPie4: IconType;
export declare const TbChartPieOff: IconType;
export declare const TbChartPie: IconType;
export declare const TbChartPpf: IconType;
export declare const TbChartRadar: IconType;
export declare const TbChartSankey: IconType;
export declare const TbChartScatter3D: IconType;
export declare const TbChartScatter: IconType;
export declare const TbChartTreemap: IconType;
export declare const TbCheck: IconType;
export declare const TbCheckbox: IconType;
export declare const TbChecklist: IconType;
export declare const TbChecks: IconType;
export declare const TbCheckupList: IconType;
export declare const TbCheese: IconType;
export declare const TbChefHatOff: IconType;
export declare const TbChefHat: IconType;
export declare const TbCherry: IconType;
export declare const TbChessBishop: IconType;
export declare const TbChessKing: IconType;
export declare const TbChessKnight: IconType;
export declare const TbChessQueen: IconType;
export declare const TbChessRook: IconType;
export declare const TbChess: IconType;
export declare const TbChevronCompactDown: IconType;
export declare const TbChevronCompactLeft: IconType;
export declare const TbChevronCompactRight: IconType;
export declare const TbChevronCompactUp: IconType;
export declare const TbChevronDownLeft: IconType;
export declare const TbChevronDownRight: IconType;
export declare const TbChevronDown: IconType;
export declare const TbChevronLeftPipe: IconType;
export declare const TbChevronLeft: IconType;
export declare const TbChevronRightPipe: IconType;
export declare const TbChevronRight: IconType;
export declare const TbChevronUpLeft: IconType;
export declare const TbChevronUpRight: IconType;
export declare const TbChevronUp: IconType;
export declare const TbChevronsDownLeft: IconType;
export declare const TbChevronsDownRight: IconType;
export declare const TbChevronsDown: IconType;
export declare const TbChevronsLeft: IconType;
export declare const TbChevronsRight: IconType;
export declare const TbChevronsUpLeft: IconType;
export declare const TbChevronsUpRight: IconType;
export declare const TbChevronsUp: IconType;
export declare const TbChisel: IconType;
export declare const TbChristmasBall: IconType;
export declare const TbChristmasTreeOff: IconType;
export declare const TbChristmasTree: IconType;
export declare const TbCircleArrowDownLeft: IconType;
export declare const TbCircleArrowDownRight: IconType;
export declare const TbCircleArrowDown: IconType;
export declare const TbCircleArrowLeft: IconType;
export declare const TbCircleArrowRight: IconType;
export declare const TbCircleArrowUpLeft: IconType;
export declare const TbCircleArrowUpRight: IconType;
export declare const TbCircleArrowUp: IconType;
export declare const TbCircleCaretDown: IconType;
export declare const TbCircleCaretLeft: IconType;
export declare const TbCircleCaretRight: IconType;
export declare const TbCircleCaretUp: IconType;
export declare const TbCircleCheck: IconType;
export declare const TbCircleChevronDown: IconType;
export declare const TbCircleChevronLeft: IconType;
export declare const TbCircleChevronRight: IconType;
export declare const TbCircleChevronUp: IconType;
export declare const TbCircleChevronsDown: IconType;
export declare const TbCircleChevronsLeft: IconType;
export declare const TbCircleChevronsRight: IconType;
export declare const TbCircleChevronsUp: IconType;
export declare const TbCircleDashedCheck: IconType;
export declare const TbCircleDashedLetterA: IconType;
export declare const TbCircleDashedLetterB: IconType;
export declare const TbCircleDashedLetterC: IconType;
export declare const TbCircleDashedLetterD: IconType;
export declare const TbCircleDashedLetterE: IconType;
export declare const TbCircleDashedLetterF: IconType;
export declare const TbCircleDashedLetterG: IconType;
export declare const TbCircleDashedLetterH: IconType;
export declare const TbCircleDashedLetterI: IconType;
export declare const TbCircleDashedLetterJ: IconType;
export declare const TbCircleDashedLetterK: IconType;
export declare const TbCircleDashedLetterL: IconType;
export declare const TbCircleDashedLetterM: IconType;
export declare const TbCircleDashedLetterN: IconType;
export declare const TbCircleDashedLetterO: IconType;
export declare const TbCircleDashedLetterP: IconType;
export declare const TbCircleDashedLetterQ: IconType;
export declare const TbCircleDashedLetterR: IconType;
export declare const TbCircleDashedLetterS: IconType;
export declare const TbCircleDashedLetterT: IconType;
export declare const TbCircleDashedLetterU: IconType;
export declare const TbCircleDashedLetterV: IconType;
export declare const TbCircleDashedLetterW: IconType;
export declare const TbCircleDashedLetterX: IconType;
export declare const TbCircleDashedLetterY: IconType;
export declare const TbCircleDashedLetterZ: IconType;
export declare const TbCircleDashedMinus: IconType;
export declare const TbCircleDashedNumber0: IconType;
export declare const TbCircleDashedNumber1: IconType;
export declare const TbCircleDashedNumber2: IconType;
export declare const TbCircleDashedNumber3: IconType;
export declare const TbCircleDashedNumber4: IconType;
export declare const TbCircleDashedNumber5: IconType;
export declare const TbCircleDashedNumber6: IconType;
export declare const TbCircleDashedNumber7: IconType;
export declare const TbCircleDashedNumber8: IconType;
export declare const TbCircleDashedNumber9: IconType;
export declare const TbCircleDashedPercentage: IconType;
export declare const TbCircleDashedPlus: IconType;
export declare const TbCircleDashedX: IconType;
export declare const TbCircleDashed: IconType;
export declare const TbCircleDot: IconType;
export declare const TbCircleDottedLetterA: IconType;
export declare const TbCircleDottedLetterB: IconType;
export declare const TbCircleDottedLetterC: IconType;
export declare const TbCircleDottedLetterD: IconType;
export declare const TbCircleDottedLetterE: IconType;
export declare const TbCircleDottedLetterF: IconType;
export declare const TbCircleDottedLetterG: IconType;
export declare const TbCircleDottedLetterH: IconType;
export declare const TbCircleDottedLetterI: IconType;
export declare const TbCircleDottedLetterJ: IconType;
export declare const TbCircleDottedLetterK: IconType;
export declare const TbCircleDottedLetterL: IconType;
export declare const TbCircleDottedLetterM: IconType;
export declare const TbCircleDottedLetterN: IconType;
export declare const TbCircleDottedLetterO: IconType;
export declare const TbCircleDottedLetterP: IconType;
export declare const TbCircleDottedLetterQ: IconType;
export declare const TbCircleDottedLetterR: IconType;
export declare const TbCircleDottedLetterS: IconType;
export declare const TbCircleDottedLetterT: IconType;
export declare const TbCircleDottedLetterU: IconType;
export declare const TbCircleDottedLetterV: IconType;
export declare const TbCircleDottedLetterW: IconType;
export declare const TbCircleDottedLetterX: IconType;
export declare const TbCircleDottedLetterY: IconType;
export declare const TbCircleDottedLetterZ: IconType;
export declare const TbCircleDotted: IconType;
export declare const TbCircleHalf2: IconType;
export declare const TbCircleHalfVertical: IconType;
export declare const TbCircleHalf: IconType;
export declare const TbCircleKey: IconType;
export declare const TbCircleLetterA: IconType;
export declare const TbCircleLetterB: IconType;
export declare const TbCircleLetterC: IconType;
export declare const TbCircleLetterD: IconType;
export declare const TbCircleLetterE: IconType;
export declare const TbCircleLetterF: IconType;
export declare const TbCircleLetterG: IconType;
export declare const TbCircleLetterH: IconType;
export declare const TbCircleLetterI: IconType;
export declare const TbCircleLetterJ: IconType;
export declare const TbCircleLetterK: IconType;
export declare const TbCircleLetterL: IconType;
export declare const TbCircleLetterM: IconType;
export declare const TbCircleLetterN: IconType;
export declare const TbCircleLetterO: IconType;
export declare const TbCircleLetterP: IconType;
export declare const TbCircleLetterQ: IconType;
export declare const TbCircleLetterR: IconType;
export declare const TbCircleLetterS: IconType;
export declare const TbCircleLetterT: IconType;
export declare const TbCircleLetterU: IconType;
export declare const TbCircleLetterV: IconType;
export declare const TbCircleLetterW: IconType;
export declare const TbCircleLetterX: IconType;
export declare const TbCircleLetterY: IconType;
export declare const TbCircleLetterZ: IconType;
export declare const TbCircleMinus2: IconType;
export declare const TbCircleMinus: IconType;
export declare const TbCircleNumber0: IconType;
export declare const TbCircleNumber1: IconType;
export declare const TbCircleNumber2: IconType;
export declare const TbCircleNumber3: IconType;
export declare const TbCircleNumber4: IconType;
export declare const TbCircleNumber5: IconType;
export declare const TbCircleNumber6: IconType;
export declare const TbCircleNumber7: IconType;
export declare const TbCircleNumber8: IconType;
export declare const TbCircleNumber9: IconType;
export declare const TbCircleOff: IconType;
export declare const TbCirclePercentage: IconType;
export declare const TbCirclePlus2: IconType;
export declare const TbCirclePlus: IconType;
export declare const TbCircleRectangleOff: IconType;
export declare const TbCircleRectangle: IconType;
export declare const TbCircleSquare: IconType;
export declare const TbCircleTriangle: IconType;
export declare const TbCircleX: IconType;
export declare const TbCircle: IconType;
export declare const TbCirclesRelation: IconType;
export declare const TbCircles: IconType;
export declare const TbCircuitAmmeter: IconType;
export declare const TbCircuitBattery: IconType;
export declare const TbCircuitBulb: IconType;
export declare const TbCircuitCapacitorPolarized: IconType;
export declare const TbCircuitCapacitor: IconType;
export declare const TbCircuitCellPlus: IconType;
export declare const TbCircuitCell: IconType;
export declare const TbCircuitChangeover: IconType;
export declare const TbCircuitDiodeZener: IconType;
export declare const TbCircuitDiode: IconType;
export declare const TbCircuitGroundDigital: IconType;
export declare const TbCircuitGround: IconType;
export declare const TbCircuitInductor: IconType;
export declare const TbCircuitMotor: IconType;
export declare const TbCircuitPushbutton: IconType;
export declare const TbCircuitResistor: IconType;
export declare const TbCircuitSwitchClosed: IconType;
export declare const TbCircuitSwitchOpen: IconType;
export declare const TbCircuitVoltmeter: IconType;
export declare const TbClearAll: IconType;
export declare const TbClearFormatting: IconType;
export declare const TbClick: IconType;
export declare const TbCliffJumping: IconType;
export declare const TbClipboardCheck: IconType;
export declare const TbClipboardCopy: IconType;
export declare const TbClipboardData: IconType;
export declare const TbClipboardHeart: IconType;
export declare const TbClipboardList: IconType;
export declare const TbClipboardOff: IconType;
export declare const TbClipboardPlus: IconType;
export declare const TbClipboardSearch: IconType;
export declare const TbClipboardSmile: IconType;
export declare const TbClipboardText: IconType;
export declare const TbClipboardTypography: IconType;
export declare const TbClipboardX: IconType;
export declare const TbClipboard: IconType;
export declare const TbClock12: IconType;
export declare const TbClock2: IconType;
export declare const TbClock24: IconType;
export declare const TbClockBitcoin: IconType;
export declare const TbClockBolt: IconType;
export declare const TbClockCancel: IconType;
export declare const TbClockCheck: IconType;
export declare const TbClockCode: IconType;
export declare const TbClockCog: IconType;
export declare const TbClockDollar: IconType;
export declare const TbClockDown: IconType;
export declare const TbClockEdit: IconType;
export declare const TbClockExclamation: IconType;
export declare const TbClockHeart: IconType;
export declare const TbClockHour1: IconType;
export declare const TbClockHour10: IconType;
export declare const TbClockHour11: IconType;
export declare const TbClockHour12: IconType;
export declare const TbClockHour2: IconType;
export declare const TbClockHour3: IconType;
export declare const TbClockHour4: IconType;
export declare const TbClockHour5: IconType;
export declare const TbClockHour6: IconType;
export declare const TbClockHour7: IconType;
export declare const TbClockHour8: IconType;
export declare const TbClockHour9: IconType;
export declare const TbClockMinus: IconType;
export declare const TbClockOff: IconType;
export declare const TbClockPause: IconType;
export declare const TbClockPin: IconType;
export declare const TbClockPlay: IconType;
export declare const TbClockPlus: IconType;
export declare const TbClockQuestion: IconType;
export declare const TbClockRecord: IconType;
export declare const TbClockSearch: IconType;
export declare const TbClockShare: IconType;
export declare const TbClockShield: IconType;
export declare const TbClockStar: IconType;
export declare const TbClockStop: IconType;
export declare const TbClockUp: IconType;
export declare const TbClockX: IconType;
export declare const TbClock: IconType;
export declare const TbClothesRackOff: IconType;
export declare const TbClothesRack: IconType;
export declare const TbCloudBitcoin: IconType;
export declare const TbCloudBolt: IconType;
export declare const TbCloudCancel: IconType;
export declare const TbCloudCheck: IconType;
export declare const TbCloudCode: IconType;
export declare const TbCloudCog: IconType;
export declare const TbCloudComputing: IconType;
export declare const TbCloudDataConnection: IconType;
export declare const TbCloudDollar: IconType;
export declare const TbCloudDown: IconType;
export declare const TbCloudDownload: IconType;
export declare const TbCloudExclamation: IconType;
export declare const TbCloudFog: IconType;
export declare const TbCloudHeart: IconType;
export declare const TbCloudLockOpen: IconType;
export declare const TbCloudLock: IconType;
export declare const TbCloudMinus: IconType;
export declare const TbCloudNetwork: IconType;
export declare const TbCloudOff: IconType;
export declare const TbCloudPause: IconType;
export declare const TbCloudPin: IconType;
export declare const TbCloudPlus: IconType;
export declare const TbCloudQuestion: IconType;
export declare const TbCloudRain: IconType;
export declare const TbCloudSearch: IconType;
export declare const TbCloudShare: IconType;
export declare const TbCloudSnow: IconType;
export declare const TbCloudStar: IconType;
export declare const TbCloudStorm: IconType;
export declare const TbCloudUp: IconType;
export declare const TbCloudUpload: IconType;
export declare const TbCloudX: IconType;
export declare const TbCloud: IconType;
export declare const TbClover2: IconType;
export declare const TbClover: IconType;
export declare const TbClubs: IconType;
export declare const TbCodeAsterisk: IconType;
export declare const TbCodeCircle2: IconType;
export declare const TbCodeCircle: IconType;
export declare const TbCodeDots: IconType;
export declare const TbCodeMinus: IconType;
export declare const TbCodeOff: IconType;
export declare const TbCodePlus: IconType;
export declare const TbCodeVariableMinus: IconType;
export declare const TbCodeVariablePlus: IconType;
export declare const TbCodeVariable: IconType;
export declare const TbCode: IconType;
export declare const TbCoffeeOff: IconType;
export declare const TbCoffee: IconType;
export declare const TbCoffin: IconType;
export declare const TbCoinBitcoin: IconType;
export declare const TbCoinEuro: IconType;
export declare const TbCoinMonero: IconType;
export declare const TbCoinOff: IconType;
export declare const TbCoinPound: IconType;
export declare const TbCoinRupee: IconType;
export declare const TbCoinTaka: IconType;
export declare const TbCoinYen: IconType;
export declare const TbCoinYuan: IconType;
export declare const TbCoin: IconType;
export declare const TbCoins: IconType;
export declare const TbColorFilter: IconType;
export declare const TbColorPickerOff: IconType;
export declare const TbColorPicker: IconType;
export declare const TbColorSwatchOff: IconType;
export declare const TbColorSwatch: IconType;
export declare const TbColumnInsertLeft: IconType;
export declare const TbColumnInsertRight: IconType;
export declare const TbColumnRemove: IconType;
export declare const TbColumns1: IconType;
export declare const TbColumns2: IconType;
export declare const TbColumns3: IconType;
export declare const TbColumnsOff: IconType;
export declare const TbColumns: IconType;
export declare const TbComet: IconType;
export declare const TbCommandOff: IconType;
export declare const TbCommand: IconType;
export declare const TbCompassOff: IconType;
export declare const TbCompass: IconType;
export declare const TbComponentsOff: IconType;
export declare const TbComponents: IconType;
export declare const TbCone2: IconType;
export declare const TbConeOff: IconType;
export declare const TbConePlus: IconType;
export declare const TbCone: IconType;
export declare const TbConfettiOff: IconType;
export declare const TbConfetti: IconType;
export declare const TbConfucius: IconType;
export declare const TbCongruentTo: IconType;
export declare const TbContainerOff: IconType;
export declare const TbContainer: IconType;
export declare const TbContract: IconType;
export declare const TbContrast2Off: IconType;
export declare const TbContrast2: IconType;
export declare const TbContrastOff: IconType;
export declare const TbContrast: IconType;
export declare const TbCooker: IconType;
export declare const TbCookieMan: IconType;
export declare const TbCookieOff: IconType;
export declare const TbCookie: IconType;
export declare const TbCopyCheck: IconType;
export declare const TbCopyMinus: IconType;
export declare const TbCopyOff: IconType;
export declare const TbCopyPlus: IconType;
export declare const TbCopyX: IconType;
export declare const TbCopy: IconType;
export declare const TbCopyleftOff: IconType;
export declare const TbCopyleft: IconType;
export declare const TbCopyrightOff: IconType;
export declare const TbCopyright: IconType;
export declare const TbCornerDownLeftDouble: IconType;
export declare const TbCornerDownLeft: IconType;
export declare const TbCornerDownRightDouble: IconType;
export declare const TbCornerDownRight: IconType;
export declare const TbCornerLeftDownDouble: IconType;
export declare const TbCornerLeftDown: IconType;
export declare const TbCornerLeftUpDouble: IconType;
export declare const TbCornerLeftUp: IconType;
export declare const TbCornerRightDownDouble: IconType;
export declare const TbCornerRightDown: IconType;
export declare const TbCornerRightUpDouble: IconType;
export declare const TbCornerRightUp: IconType;
export declare const TbCornerUpLeftDouble: IconType;
export declare const TbCornerUpLeft: IconType;
export declare const TbCornerUpRightDouble: IconType;
export declare const TbCornerUpRight: IconType;
export declare const TbCpu2: IconType;
export declare const TbCpuOff: IconType;
export declare const TbCpu: IconType;
export declare const TbCraneOff: IconType;
export declare const TbCrane: IconType;
export declare const TbCreativeCommonsBy: IconType;
export declare const TbCreativeCommonsNc: IconType;
export declare const TbCreativeCommonsNd: IconType;
export declare const TbCreativeCommonsOff: IconType;
export declare const TbCreativeCommonsSa: IconType;
export declare const TbCreativeCommonsZero: IconType;
export declare const TbCreativeCommons: IconType;
export declare const TbCreditCardOff: IconType;
export declare const TbCreditCardPay: IconType;
export declare const TbCreditCardRefund: IconType;
export declare const TbCreditCard: IconType;
export declare const TbCricket: IconType;
export declare const TbCrop11: IconType;
export declare const TbCrop169: IconType;
export declare const TbCrop32: IconType;
export declare const TbCrop54: IconType;
export declare const TbCrop75: IconType;
export declare const TbCropLandscape: IconType;
export declare const TbCropPortrait: IconType;
export declare const TbCrop: IconType;
export declare const TbCrossOff: IconType;
export declare const TbCross: IconType;
export declare const TbCrosshair: IconType;
export declare const TbCrownOff: IconType;
export declare const TbCrown: IconType;
export declare const TbCrutchesOff: IconType;
export declare const TbCrutches: IconType;
export declare const TbCrystalBall: IconType;
export declare const TbCsv: IconType;
export declare const TbCube3dSphereOff: IconType;
export declare const TbCube3dSphere: IconType;
export declare const TbCubeOff: IconType;
export declare const TbCubePlus: IconType;
export declare const TbCubeSend: IconType;
export declare const TbCubeSpark: IconType;
export declare const TbCubeUnfolded: IconType;
export declare const TbCube: IconType;
export declare const TbCupOff: IconType;
export declare const TbCup: IconType;
export declare const TbCurling: IconType;
export declare const TbCurlyLoop: IconType;
export declare const TbCurrencyAfghani: IconType;
export declare const TbCurrencyBahraini: IconType;
export declare const TbCurrencyBaht: IconType;
export declare const TbCurrencyBitcoin: IconType;
export declare const TbCurrencyCent: IconType;
export declare const TbCurrencyDinar: IconType;
export declare const TbCurrencyDirham: IconType;
export declare const TbCurrencyDogecoin: IconType;
export declare const TbCurrencyDollarAustralian: IconType;
export declare const TbCurrencyDollarBrunei: IconType;
export declare const TbCurrencyDollarCanadian: IconType;
export declare const TbCurrencyDollarGuyanese: IconType;
export declare const TbCurrencyDollarOff: IconType;
export declare const TbCurrencyDollarSingapore: IconType;
export declare const TbCurrencyDollarZimbabwean: IconType;
export declare const TbCurrencyDollar: IconType;
export declare const TbCurrencyDong: IconType;
export declare const TbCurrencyDram: IconType;
export declare const TbCurrencyEthereum: IconType;
export declare const TbCurrencyEuroOff: IconType;
export declare const TbCurrencyEuro: IconType;
export declare const TbCurrencyFlorin: IconType;
export declare const TbCurrencyForint: IconType;
export declare const TbCurrencyFrank: IconType;
export declare const TbCurrencyGuarani: IconType;
export declare const TbCurrencyHryvnia: IconType;
export declare const TbCurrencyIranianRial: IconType;
export declare const TbCurrencyKip: IconType;
export declare const TbCurrencyKroneCzech: IconType;
export declare const TbCurrencyKroneDanish: IconType;
export declare const TbCurrencyKroneSwedish: IconType;
export declare const TbCurrencyLari: IconType;
export declare const TbCurrencyLeu: IconType;
export declare const TbCurrencyLira: IconType;
export declare const TbCurrencyLitecoin: IconType;
export declare const TbCurrencyLyd: IconType;
export declare const TbCurrencyManat: IconType;
export declare const TbCurrencyMonero: IconType;
export declare const TbCurrencyNaira: IconType;
export declare const TbCurrencyNano: IconType;
export declare const TbCurrencyOff: IconType;
export declare const TbCurrencyPaanga: IconType;
export declare const TbCurrencyPeso: IconType;
export declare const TbCurrencyPoundOff: IconType;
export declare const TbCurrencyPound: IconType;
export declare const TbCurrencyQuetzal: IconType;
export declare const TbCurrencyReal: IconType;
export declare const TbCurrencyRenminbi: IconType;
export declare const TbCurrencyRipple: IconType;
export declare const TbCurrencyRiyal: IconType;
export declare const TbCurrencyRubel: IconType;
export declare const TbCurrencyRufiyaa: IconType;
export declare const TbCurrencyRupeeNepalese: IconType;
export declare const TbCurrencyRupee: IconType;
export declare const TbCurrencyShekel: IconType;
export declare const TbCurrencySolana: IconType;
export declare const TbCurrencySom: IconType;
export declare const TbCurrencyTaka: IconType;
export declare const TbCurrencyTenge: IconType;
export declare const TbCurrencyTugrik: IconType;
export declare const TbCurrencyWon: IconType;
export declare const TbCurrencyXrp: IconType;
export declare const TbCurrencyYenOff: IconType;
export declare const TbCurrencyYen: IconType;
export declare const TbCurrencyYuan: IconType;
export declare const TbCurrencyZloty: IconType;
export declare const TbCurrency: IconType;
export declare const TbCurrentLocationOff: IconType;
export declare const TbCurrentLocation: IconType;
export declare const TbCursorOff: IconType;
export declare const TbCursorText: IconType;
export declare const TbCut: IconType;
export declare const TbCylinderOff: IconType;
export declare const TbCylinderPlus: IconType;
export declare const TbCylinder: IconType;
export declare const TbDashboardOff: IconType;
export declare const TbDashboard: IconType;
export declare const TbDatabaseCog: IconType;
export declare const TbDatabaseDollar: IconType;
export declare const TbDatabaseEdit: IconType;
export declare const TbDatabaseExclamation: IconType;
export declare const TbDatabaseExport: IconType;
export declare const TbDatabaseHeart: IconType;
export declare const TbDatabaseImport: IconType;
export declare const TbDatabaseLeak: IconType;
export declare const TbDatabaseMinus: IconType;
export declare const TbDatabaseOff: IconType;
export declare const TbDatabasePlus: IconType;
export declare const TbDatabaseSearch: IconType;
export declare const TbDatabaseShare: IconType;
export declare const TbDatabaseSmile: IconType;
export declare const TbDatabaseStar: IconType;
export declare const TbDatabaseX: IconType;
export declare const TbDatabase: IconType;
export declare const TbDecimal: IconType;
export declare const TbDeer: IconType;
export declare const TbDelta: IconType;
export declare const TbDentalBroken: IconType;
export declare const TbDentalOff: IconType;
export declare const TbDental: IconType;
export declare const TbDeselect: IconType;
export declare const TbDesk: IconType;
export declare const TbDetailsOff: IconType;
export declare const TbDetails: IconType;
export declare const TbDeviceAirpodsCase: IconType;
export declare const TbDeviceAirpods: IconType;
export declare const TbDeviceAirtag: IconType;
export declare const TbDeviceAnalytics: IconType;
export declare const TbDeviceAudioTape: IconType;
export declare const TbDeviceCameraPhone: IconType;
export declare const TbDeviceCctvOff: IconType;
export declare const TbDeviceCctv: IconType;
export declare const TbDeviceComputerCameraOff: IconType;
export declare const TbDeviceComputerCamera: IconType;
export declare const TbDeviceDesktopAnalytics: IconType;
export declare const TbDeviceDesktopBolt: IconType;
export declare const TbDeviceDesktopCancel: IconType;
export declare const TbDeviceDesktopCheck: IconType;
export declare const TbDeviceDesktopCode: IconType;
export declare const TbDeviceDesktopCog: IconType;
export declare const TbDeviceDesktopDollar: IconType;
export declare const TbDeviceDesktopDown: IconType;
export declare const TbDeviceDesktopExclamation: IconType;
export declare const TbDeviceDesktopHeart: IconType;
export declare const TbDeviceDesktopMinus: IconType;
export declare const TbDeviceDesktopOff: IconType;
export declare const TbDeviceDesktopPause: IconType;
export declare const TbDeviceDesktopPin: IconType;
export declare const TbDeviceDesktopPlus: IconType;
export declare const TbDeviceDesktopQuestion: IconType;
export declare const TbDeviceDesktopSearch: IconType;
export declare const TbDeviceDesktopShare: IconType;
export declare const TbDeviceDesktopStar: IconType;
export declare const TbDeviceDesktopUp: IconType;
export declare const TbDeviceDesktopX: IconType;
export declare const TbDeviceDesktop: IconType;
export declare const TbDeviceFloppy: IconType;
export declare const TbDeviceGamepad2: IconType;
export declare const TbDeviceGamepad3: IconType;
export declare const TbDeviceGamepad: IconType;
export declare const TbDeviceHeartMonitor: IconType;
export declare const TbDeviceImacBolt: IconType;
export declare const TbDeviceImacCancel: IconType;
export declare const TbDeviceImacCheck: IconType;
export declare const TbDeviceImacCode: IconType;
export declare const TbDeviceImacCog: IconType;
export declare const TbDeviceImacDollar: IconType;
export declare const TbDeviceImacDown: IconType;
export declare const TbDeviceImacExclamation: IconType;
export declare const TbDeviceImacHeart: IconType;
export declare const TbDeviceImacMinus: IconType;
export declare const TbDeviceImacOff: IconType;
export declare const TbDeviceImacPause: IconType;
export declare const TbDeviceImacPin: IconType;
export declare const TbDeviceImacPlus: IconType;
export declare const TbDeviceImacQuestion: IconType;
export declare const TbDeviceImacSearch: IconType;
export declare const TbDeviceImacShare: IconType;
export declare const TbDeviceImacStar: IconType;
export declare const TbDeviceImacUp: IconType;
export declare const TbDeviceImacX: IconType;
export declare const TbDeviceImac: IconType;
export declare const TbDeviceIpadBolt: IconType;
export declare const TbDeviceIpadCancel: IconType;
export declare const TbDeviceIpadCheck: IconType;
export declare const TbDeviceIpadCode: IconType;
export declare const TbDeviceIpadCog: IconType;
export declare const TbDeviceIpadDollar: IconType;
export declare const TbDeviceIpadDown: IconType;
export declare const TbDeviceIpadExclamation: IconType;
export declare const TbDeviceIpadHeart: IconType;
export declare const TbDeviceIpadHorizontalBolt: IconType;
export declare const TbDeviceIpadHorizontalCancel: IconType;
export declare const TbDeviceIpadHorizontalCheck: IconType;
export declare const TbDeviceIpadHorizontalCode: IconType;
export declare const TbDeviceIpadHorizontalCog: IconType;
export declare const TbDeviceIpadHorizontalDollar: IconType;
export declare const TbDeviceIpadHorizontalDown: IconType;
export declare const TbDeviceIpadHorizontalExclamation: IconType;
export declare const TbDeviceIpadHorizontalHeart: IconType;
export declare const TbDeviceIpadHorizontalMinus: IconType;
export declare const TbDeviceIpadHorizontalOff: IconType;
export declare const TbDeviceIpadHorizontalPause: IconType;
export declare const TbDeviceIpadHorizontalPin: IconType;
export declare const TbDeviceIpadHorizontalPlus: IconType;
export declare const TbDeviceIpadHorizontalQuestion: IconType;
export declare const TbDeviceIpadHorizontalSearch: IconType;
export declare const TbDeviceIpadHorizontalShare: IconType;
export declare const TbDeviceIpadHorizontalStar: IconType;
export declare const TbDeviceIpadHorizontalUp: IconType;
export declare const TbDeviceIpadHorizontalX: IconType;
export declare const TbDeviceIpadHorizontal: IconType;
export declare const TbDeviceIpadMinus: IconType;
export declare const TbDeviceIpadOff: IconType;
export declare const TbDeviceIpadPause: IconType;
export declare const TbDeviceIpadPin: IconType;
export declare const TbDeviceIpadPlus: IconType;
export declare const TbDeviceIpadQuestion: IconType;
export declare const TbDeviceIpadSearch: IconType;
export declare const TbDeviceIpadShare: IconType;
export declare const TbDeviceIpadStar: IconType;
export declare const TbDeviceIpadUp: IconType;
export declare const TbDeviceIpadX: IconType;
export declare const TbDeviceIpad: IconType;
export declare const TbDeviceLandlinePhone: IconType;
export declare const TbDeviceLaptopOff: IconType;
export declare const TbDeviceLaptop: IconType;
export declare const TbDeviceMobileBolt: IconType;
export declare const TbDeviceMobileCancel: IconType;
export declare const TbDeviceMobileCharging: IconType;
export declare const TbDeviceMobileCheck: IconType;
export declare const TbDeviceMobileCode: IconType;
export declare const TbDeviceMobileCog: IconType;
export declare const TbDeviceMobileDollar: IconType;
export declare const TbDeviceMobileDown: IconType;
export declare const TbDeviceMobileExclamation: IconType;
export declare const TbDeviceMobileHeart: IconType;
export declare const TbDeviceMobileMessage: IconType;
export declare const TbDeviceMobileMinus: IconType;
export declare const TbDeviceMobileOff: IconType;
export declare const TbDeviceMobilePause: IconType;
export declare const TbDeviceMobilePin: IconType;
export declare const TbDeviceMobilePlus: IconType;
export declare const TbDeviceMobileQuestion: IconType;
export declare const TbDeviceMobileRotated: IconType;
export declare const TbDeviceMobileSearch: IconType;
export declare const TbDeviceMobileShare: IconType;
export declare const TbDeviceMobileStar: IconType;
export declare const TbDeviceMobileUp: IconType;
export declare const TbDeviceMobileVibration: IconType;
export declare const TbDeviceMobileX: IconType;
export declare const TbDeviceMobile: IconType;
export declare const TbDeviceNintendoOff: IconType;
export declare const TbDeviceNintendo: IconType;
export declare const TbDeviceProjector: IconType;
export declare const TbDeviceRemote: IconType;
export declare const TbDeviceSdCard: IconType;
export declare const TbDeviceSim1: IconType;
export declare const TbDeviceSim2: IconType;
export declare const TbDeviceSim3: IconType;
export declare const TbDeviceSim: IconType;
export declare const TbDeviceSpeakerOff: IconType;
export declare const TbDeviceSpeaker: IconType;
export declare const TbDeviceTabletBolt: IconType;
export declare const TbDeviceTabletCancel: IconType;
export declare const TbDeviceTabletCheck: IconType;
export declare const TbDeviceTabletCode: IconType;
export declare const TbDeviceTabletCog: IconType;
export declare const TbDeviceTabletDollar: IconType;
export declare const TbDeviceTabletDown: IconType;
export declare const TbDeviceTabletExclamation: IconType;
export declare const TbDeviceTabletHeart: IconType;
export declare const TbDeviceTabletMinus: IconType;
export declare const TbDeviceTabletOff: IconType;
export declare const TbDeviceTabletPause: IconType;
export declare const TbDeviceTabletPin: IconType;
export declare const TbDeviceTabletPlus: IconType;
export declare const TbDeviceTabletQuestion: IconType;
export declare const TbDeviceTabletSearch: IconType;
export declare const TbDeviceTabletShare: IconType;
export declare const TbDeviceTabletStar: IconType;
export declare const TbDeviceTabletUp: IconType;
export declare const TbDeviceTabletX: IconType;
export declare const TbDeviceTablet: IconType;
export declare const TbDeviceTvOff: IconType;
export declare const TbDeviceTvOld: IconType;
export declare const TbDeviceTv: IconType;
export declare const TbDeviceUnknown: IconType;
export declare const TbDeviceUsb: IconType;
export declare const TbDeviceVisionPro: IconType;
export declare const TbDeviceWatchBolt: IconType;
export declare const TbDeviceWatchCancel: IconType;
export declare const TbDeviceWatchCheck: IconType;
export declare const TbDeviceWatchCode: IconType;
export declare const TbDeviceWatchCog: IconType;
export declare const TbDeviceWatchDollar: IconType;
export declare const TbDeviceWatchDown: IconType;
export declare const TbDeviceWatchExclamation: IconType;
export declare const TbDeviceWatchHeart: IconType;
export declare const TbDeviceWatchMinus: IconType;
export declare const TbDeviceWatchOff: IconType;
export declare const TbDeviceWatchPause: IconType;
export declare const TbDeviceWatchPin: IconType;
export declare const TbDeviceWatchPlus: IconType;
export declare const TbDeviceWatchQuestion: IconType;
export declare const TbDeviceWatchSearch: IconType;
export declare const TbDeviceWatchShare: IconType;
export declare const TbDeviceWatchStar: IconType;
export declare const TbDeviceWatchStats2: IconType;
export declare const TbDeviceWatchStats: IconType;
export declare const TbDeviceWatchUp: IconType;
export declare const TbDeviceWatchX: IconType;
export declare const TbDeviceWatch: IconType;
export declare const TbDevices2: IconType;
export declare const TbDevicesBolt: IconType;
export declare const TbDevicesCancel: IconType;
export declare const TbDevicesCheck: IconType;
export declare const TbDevicesCode: IconType;
export declare const TbDevicesCog: IconType;
export declare const TbDevicesDollar: IconType;
export declare const TbDevicesDown: IconType;
export declare const TbDevicesExclamation: IconType;
export declare const TbDevicesHeart: IconType;
export declare const TbDevicesMinus: IconType;
export declare const TbDevicesOff: IconType;
export declare const TbDevicesPause: IconType;
export declare const TbDevicesPcOff: IconType;
export declare const TbDevicesPc: IconType;
export declare const TbDevicesPin: IconType;
export declare const TbDevicesPlus: IconType;
export declare const TbDevicesQuestion: IconType;
export declare const TbDevicesSearch: IconType;
export declare const TbDevicesShare: IconType;
export declare const TbDevicesStar: IconType;
export declare const TbDevicesUp: IconType;
export declare const TbDevicesX: IconType;
export declare const TbDevices: IconType;
export declare const TbDiaboloOff: IconType;
export declare const TbDiaboloPlus: IconType;
export declare const TbDiabolo: IconType;
export declare const TbDialpadOff: IconType;
export declare const TbDialpad: IconType;
export declare const TbDiamondOff: IconType;
export declare const TbDiamond: IconType;
export declare const TbDiamonds: IconType;
export declare const TbDiaper: IconType;
export declare const TbDice1: IconType;
export declare const TbDice2: IconType;
export declare const TbDice3: IconType;
export declare const TbDice4: IconType;
export declare const TbDice5: IconType;
export declare const TbDice6: IconType;
export declare const TbDice: IconType;
export declare const TbDimensions: IconType;
export declare const TbDirectionArrows: IconType;
export declare const TbDirectionHorizontal: IconType;
export declare const TbDirectionSignOff: IconType;
export declare const TbDirectionSign: IconType;
export declare const TbDirection: IconType;
export declare const TbDirectionsOff: IconType;
export declare const TbDirections: IconType;
export declare const TbDisabled2: IconType;
export declare const TbDisabledOff: IconType;
export declare const TbDisabled: IconType;
export declare const TbDiscGolf: IconType;
export declare const TbDiscOff: IconType;
export declare const TbDisc: IconType;
export declare const TbDiscountOff: IconType;
export declare const TbDiscount: IconType;
export declare const TbDivide: IconType;
export declare const TbDna2Off: IconType;
export declare const TbDna2: IconType;
export declare const TbDnaOff: IconType;
export declare const TbDna: IconType;
export declare const TbDogBowl: IconType;
export declare const TbDog: IconType;
export declare const TbDoorEnter: IconType;
export declare const TbDoorExit: IconType;
export declare const TbDoorOff: IconType;
export declare const TbDoor: IconType;
export declare const TbDotsCircleHorizontal: IconType;
export declare const TbDotsDiagonal2: IconType;
export declare const TbDotsDiagonal: IconType;
export declare const TbDotsVertical: IconType;
export declare const TbDots: IconType;
export declare const TbDownloadOff: IconType;
export declare const TbDownload: IconType;
export declare const TbDragDrop2: IconType;
export declare const TbDragDrop: IconType;
export declare const TbDroneOff: IconType;
export declare const TbDrone: IconType;
export declare const TbDropCircle: IconType;
export declare const TbDropletBolt: IconType;
export declare const TbDropletCancel: IconType;
export declare const TbDropletCheck: IconType;
export declare const TbDropletCode: IconType;
export declare const TbDropletCog: IconType;
export declare const TbDropletDollar: IconType;
export declare const TbDropletDown: IconType;
export declare const TbDropletExclamation: IconType;
export declare const TbDropletHalf2: IconType;
export declare const TbDropletHalf: IconType;
export declare const TbDropletHeart: IconType;
export declare const TbDropletMinus: IconType;
export declare const TbDropletOff: IconType;
export declare const TbDropletPause: IconType;
export declare const TbDropletPin: IconType;
export declare const TbDropletPlus: IconType;
export declare const TbDropletQuestion: IconType;
export declare const TbDropletSearch: IconType;
export declare const TbDropletShare: IconType;
export declare const TbDropletStar: IconType;
export declare const TbDropletUp: IconType;
export declare const TbDropletX: IconType;
export declare const TbDroplet: IconType;
export declare const TbDroplets: IconType;
export declare const TbDualScreen: IconType;
export declare const TbDumpling: IconType;
export declare const TbEPassport: IconType;
export declare const TbEarOff: IconType;
export declare const TbEarScan: IconType;
export declare const TbEar: IconType;
export declare const TbEaseInControlPoint: IconType;
export declare const TbEaseInOutControlPoints: IconType;
export declare const TbEaseInOut: IconType;
export declare const TbEaseIn: IconType;
export declare const TbEaseOutControlPoint: IconType;
export declare const TbEaseOut: IconType;
export declare const TbEditCircleOff: IconType;
export declare const TbEditCircle: IconType;
export declare const TbEditOff: IconType;
export declare const TbEdit: IconType;
export declare const TbEggCracked: IconType;
export declare const TbEggFried: IconType;
export declare const TbEggOff: IconType;
export declare const TbEgg: IconType;
export declare const TbEggs: IconType;
export declare const TbElevatorOff: IconType;
export declare const TbElevator: IconType;
export declare const TbEmergencyBed: IconType;
export declare const TbEmpathizeOff: IconType;
export declare const TbEmpathize: IconType;
export declare const TbEmphasis: IconType;
export declare const TbEngineOff: IconType;
export declare const TbEngine: IconType;
export declare const TbEqualDouble: IconType;
export declare const TbEqualNot: IconType;
export declare const TbEqual: IconType;
export declare const TbEraserOff: IconType;
export declare const TbEraser: IconType;
export declare const TbError404Off: IconType;
export declare const TbError404: IconType;
export declare const TbEscalatorDown: IconType;
export declare const TbEscalatorUp: IconType;
export declare const TbEscalator: IconType;
export declare const TbExchangeOff: IconType;
export declare const TbExchange: IconType;
export declare const TbExclamationCircle: IconType;
export declare const TbExclamationMarkOff: IconType;
export declare const TbExclamationMark: IconType;
export declare const TbExplicitOff: IconType;
export declare const TbExplicit: IconType;
export declare const TbExposure0: IconType;
export declare const TbExposureMinus1: IconType;
export declare const TbExposureMinus2: IconType;
export declare const TbExposureOff: IconType;
export declare const TbExposurePlus1: IconType;
export declare const TbExposurePlus2: IconType;
export declare const TbExposure: IconType;
export declare const TbExternalLinkOff: IconType;
export declare const TbExternalLink: IconType;
export declare const TbEyeBitcoin: IconType;
export declare const TbEyeBolt: IconType;
export declare const TbEyeCancel: IconType;
export declare const TbEyeCheck: IconType;
export declare const TbEyeClosed: IconType;
export declare const TbEyeCode: IconType;
export declare const TbEyeCog: IconType;
export declare const TbEyeDiscount: IconType;
export declare const TbEyeDollar: IconType;
export declare const TbEyeDotted: IconType;
export declare const TbEyeDown: IconType;
export declare const TbEyeEdit: IconType;
export declare const TbEyeExclamation: IconType;
export declare const TbEyeHeart: IconType;
export declare const TbEyeMinus: IconType;
export declare const TbEyeOff: IconType;
export declare const TbEyePause: IconType;
export declare const TbEyePin: IconType;
export declare const TbEyePlus: IconType;
export declare const TbEyeQuestion: IconType;
export declare const TbEyeSearch: IconType;
export declare const TbEyeShare: IconType;
export declare const TbEyeSpark: IconType;
export declare const TbEyeStar: IconType;
export declare const TbEyeTable: IconType;
export declare const TbEyeUp: IconType;
export declare const TbEyeX: IconType;
export declare const TbEye: IconType;
export declare const TbEyeglass2: IconType;
export declare const TbEyeglassOff: IconType;
export declare const TbEyeglass: IconType;
export declare const TbFaceIdError: IconType;
export declare const TbFaceId: IconType;
export declare const TbFaceMaskOff: IconType;
export declare const TbFaceMask: IconType;
export declare const TbFall: IconType;
export declare const TbFavicon: IconType;
export declare const TbFeatherOff: IconType;
export declare const TbFeather: IconType;
export declare const TbFenceOff: IconType;
export declare const TbFence: IconType;
export declare const TbFerry: IconType;
export declare const TbFidgetSpinner: IconType;
export declare const TbFile3D: IconType;
export declare const TbFileAi: IconType;
export declare const TbFileAlert: IconType;
export declare const TbFileAnalytics: IconType;
export declare const TbFileArrowLeft: IconType;
export declare const TbFileArrowRight: IconType;
export declare const TbFileBarcode: IconType;
export declare const TbFileBitcoin: IconType;
export declare const TbFileBroken: IconType;
export declare const TbFileCertificate: IconType;
export declare const TbFileChart: IconType;
export declare const TbFileCheck: IconType;
export declare const TbFileCode2: IconType;
export declare const TbFileCode: IconType;
export declare const TbFileCv: IconType;
export declare const TbFileDatabase: IconType;
export declare const TbFileDelta: IconType;
export declare const TbFileDescription: IconType;
export declare const TbFileDiff: IconType;
export declare const TbFileDigit: IconType;
export declare const TbFileDislike: IconType;
export declare const TbFileDollar: IconType;
export declare const TbFileDots: IconType;
export declare const TbFileDownload: IconType;
export declare const TbFileEuro: IconType;
export declare const TbFileExcel: IconType;
export declare const TbFileExport: IconType;
export declare const TbFileFunction: IconType;
export declare const TbFileHorizontal: IconType;
export declare const TbFileImport: IconType;
export declare const TbFileInfinity: IconType;
export declare const TbFileInfo: IconType;
export declare const TbFileInvoice: IconType;
export declare const TbFileIsr: IconType;
export declare const TbFileLambda: IconType;
export declare const TbFileLike: IconType;
export declare const TbFileMinus: IconType;
export declare const TbFileMusic: IconType;
export declare const TbFileNeutral: IconType;
export declare const TbFileOff: IconType;
export declare const TbFileOrientation: IconType;
export declare const TbFilePencil: IconType;
export declare const TbFilePercent: IconType;
export declare const TbFilePhone: IconType;
export declare const TbFilePlus: IconType;
export declare const TbFilePower: IconType;
export declare const TbFileReport: IconType;
export declare const TbFileRss: IconType;
export declare const TbFileSad: IconType;
export declare const TbFileScissors: IconType;
export declare const TbFileSearch: IconType;
export declare const TbFileSettings: IconType;
export declare const TbFileShredder: IconType;
export declare const TbFileSignal: IconType;
export declare const TbFileSmile: IconType;
export declare const TbFileSpark: IconType;
export declare const TbFileSpreadsheet: IconType;
export declare const TbFileStack: IconType;
export declare const TbFileStar: IconType;
export declare const TbFileSymlink: IconType;
export declare const TbFileTextAi: IconType;
export declare const TbFileTextSpark: IconType;
export declare const TbFileText: IconType;
export declare const TbFileTime: IconType;
export declare const TbFileTypeBmp: IconType;
export declare const TbFileTypeCss: IconType;
export declare const TbFileTypeCsv: IconType;
export declare const TbFileTypeDoc: IconType;
export declare const TbFileTypeDocx: IconType;
export declare const TbFileTypeHtml: IconType;
export declare const TbFileTypeJpg: IconType;
export declare const TbFileTypeJs: IconType;
export declare const TbFileTypeJsx: IconType;
export declare const TbFileTypePdf: IconType;
export declare const TbFileTypePhp: IconType;
export declare const TbFileTypePng: IconType;
export declare const TbFileTypePpt: IconType;
export declare const TbFileTypeRs: IconType;
export declare const TbFileTypeSql: IconType;
export declare const TbFileTypeSvg: IconType;
export declare const TbFileTypeTs: IconType;
export declare const TbFileTypeTsx: IconType;
export declare const TbFileTypeTxt: IconType;
export declare const TbFileTypeVue: IconType;
export declare const TbFileTypeXls: IconType;
export declare const TbFileTypeXml: IconType;
export declare const TbFileTypeZip: IconType;
export declare const TbFileTypography: IconType;
export declare const TbFileUnknown: IconType;
export declare const TbFileUpload: IconType;
export declare const TbFileVector: IconType;
export declare const TbFileWord: IconType;
export declare const TbFileX: IconType;
export declare const TbFileZip: IconType;
export declare const TbFile: IconType;
export declare const TbFilesOff: IconType;
export declare const TbFiles: IconType;
export declare const TbFilterBolt: IconType;
export declare const TbFilterCancel: IconType;
export declare const TbFilterCheck: IconType;
export declare const TbFilterCode: IconType;
export declare const TbFilterCog: IconType;
export declare const TbFilterDiscount: IconType;
export declare const TbFilterDollar: IconType;
export declare const TbFilterDown: IconType;
export declare const TbFilterEdit: IconType;
export declare const TbFilterExclamation: IconType;
export declare const TbFilterHeart: IconType;
export declare const TbFilterMinus: IconType;
export declare const TbFilterOff: IconType;
export declare const TbFilterPause: IconType;
export declare const TbFilterPin: IconType;
export declare const TbFilterPlus: IconType;
export declare const TbFilterQuestion: IconType;
export declare const TbFilterSearch: IconType;
export declare const TbFilterShare: IconType;
export declare const TbFilterStar: IconType;
export declare const TbFilterUp: IconType;
export declare const TbFilterX: IconType;
export declare const TbFilter: IconType;
export declare const TbFilters: IconType;
export declare const TbFingerprintOff: IconType;
export declare const TbFingerprintScan: IconType;
export declare const TbFingerprint: IconType;
export declare const TbFireExtinguisher: IconType;
export declare const TbFireHydrantOff: IconType;
export declare const TbFireHydrant: IconType;
export declare const TbFiretruck: IconType;
export declare const TbFirstAidKitOff: IconType;
export declare const TbFirstAidKit: IconType;
export declare const TbFishBone: IconType;
export declare const TbFishChristianity: IconType;
export declare const TbFishHookOff: IconType;
export declare const TbFishHook: IconType;
export declare const TbFishOff: IconType;
export declare const TbFish: IconType;
export declare const TbFlag2Off: IconType;
export declare const TbFlag2: IconType;
export declare const TbFlag3: IconType;
export declare const TbFlagBitcoin: IconType;
export declare const TbFlagBolt: IconType;
export declare const TbFlagCancel: IconType;
export declare const TbFlagCheck: IconType;
export declare const TbFlagCode: IconType;
export declare const TbFlagCog: IconType;
export declare const TbFlagDiscount: IconType;
export declare const TbFlagDollar: IconType;
export declare const TbFlagDown: IconType;
export declare const TbFlagExclamation: IconType;
export declare const TbFlagHeart: IconType;
export declare const TbFlagMinus: IconType;
export declare const TbFlagOff: IconType;
export declare const TbFlagPause: IconType;
export declare const TbFlagPin: IconType;
export declare const TbFlagPlus: IconType;
export declare const TbFlagQuestion: IconType;
export declare const TbFlagSearch: IconType;
export declare const TbFlagShare: IconType;
export declare const TbFlagSpark: IconType;
export declare const TbFlagStar: IconType;
export declare const TbFlagUp: IconType;
export declare const TbFlagX: IconType;
export declare const TbFlag: IconType;
export declare const TbFlameOff: IconType;
export declare const TbFlame: IconType;
export declare const TbFlare: IconType;
export declare const TbFlask2Off: IconType;
export declare const TbFlask2: IconType;
export declare const TbFlaskOff: IconType;
export declare const TbFlask: IconType;
export declare const TbFlipFlops: IconType;
export declare const TbFlipHorizontal: IconType;
export declare const TbFlipVertical: IconType;
export declare const TbFloatCenter: IconType;
export declare const TbFloatLeft: IconType;
export declare const TbFloatNone: IconType;
export declare const TbFloatRight: IconType;
export declare const TbFlowerOff: IconType;
export declare const TbFlower: IconType;
export declare const TbFocus2: IconType;
export declare const TbFocusAuto: IconType;
export declare const TbFocusCentered: IconType;
export declare const TbFocus: IconType;
export declare const TbFoldDown: IconType;
export declare const TbFoldUp: IconType;
export declare const TbFold: IconType;
export declare const TbFolderBolt: IconType;
export declare const TbFolderCancel: IconType;
export declare const TbFolderCheck: IconType;
export declare const TbFolderCode: IconType;
export declare const TbFolderCog: IconType;
export declare const TbFolderDollar: IconType;
export declare const TbFolderDown: IconType;
export declare const TbFolderExclamation: IconType;
export declare const TbFolderHeart: IconType;
export declare const TbFolderMinus: IconType;
export declare const TbFolderOff: IconType;
export declare const TbFolderOpen: IconType;
export declare const TbFolderPause: IconType;
export declare const TbFolderPin: IconType;
export declare const TbFolderPlus: IconType;
export declare const TbFolderQuestion: IconType;
export declare const TbFolderRoot: IconType;
export declare const TbFolderSearch: IconType;
export declare const TbFolderShare: IconType;
export declare const TbFolderStar: IconType;
export declare const TbFolderSymlink: IconType;
export declare const TbFolderUp: IconType;
export declare const TbFolderX: IconType;
export declare const TbFolder: IconType;
export declare const TbFoldersOff: IconType;
export declare const TbFolders: IconType;
export declare const TbForbid2: IconType;
export declare const TbForbid: IconType;
export declare const TbForklift: IconType;
export declare const TbForms: IconType;
export declare const TbFountainOff: IconType;
export declare const TbFountain: IconType;
export declare const TbFrameOff: IconType;
export declare const TbFrame: IconType;
export declare const TbFreeRights: IconType;
export declare const TbFreezeColumn: IconType;
export declare const TbFreezeRowColumn: IconType;
export declare const TbFreezeRow: IconType;
export declare const TbFridgeOff: IconType;
export declare const TbFridge: IconType;
export declare const TbFriendsOff: IconType;
export declare const TbFriends: IconType;
export declare const TbFrustumOff: IconType;
export declare const TbFrustumPlus: IconType;
export declare const TbFrustum: IconType;
export declare const TbFunctionOff: IconType;
export declare const TbFunction: IconType;
export declare const TbGalaxy: IconType;
export declare const TbGardenCartOff: IconType;
export declare const TbGardenCart: IconType;
export declare const TbGasStationOff: IconType;
export declare const TbGasStation: IconType;
export declare const TbGaugeOff: IconType;
export declare const TbGauge: IconType;
export declare const TbGavel: IconType;
export declare const TbGenderAgender: IconType;
export declare const TbGenderAndrogyne: IconType;
export declare const TbGenderBigender: IconType;
export declare const TbGenderDemiboy: IconType;
export declare const TbGenderDemigirl: IconType;
export declare const TbGenderEpicene: IconType;
export declare const TbGenderFemale: IconType;
export declare const TbGenderFemme: IconType;
export declare const TbGenderGenderfluid: IconType;
export declare const TbGenderGenderless: IconType;
export declare const TbGenderGenderqueer: IconType;
export declare const TbGenderHermaphrodite: IconType;
export declare const TbGenderIntergender: IconType;
export declare const TbGenderMale: IconType;
export declare const TbGenderNeutrois: IconType;
export declare const TbGenderThird: IconType;
export declare const TbGenderTransgender: IconType;
export declare const TbGenderTrasvesti: IconType;
export declare const TbGeometry: IconType;
export declare const TbGhost2: IconType;
export declare const TbGhost3: IconType;
export declare const TbGhostOff: IconType;
export declare const TbGhost: IconType;
export declare const TbGif: IconType;
export declare const TbGiftCard: IconType;
export declare const TbGiftOff: IconType;
export declare const TbGift: IconType;
export declare const TbGitBranchDeleted: IconType;
export declare const TbGitBranch: IconType;
export declare const TbGitCherryPick: IconType;
export declare const TbGitCommit: IconType;
export declare const TbGitCompare: IconType;
export declare const TbGitFork: IconType;
export declare const TbGitMerge: IconType;
export declare const TbGitPullRequestClosed: IconType;
export declare const TbGitPullRequestDraft: IconType;
export declare const TbGitPullRequest: IconType;
export declare const TbGizmo: IconType;
export declare const TbGlassChampagne: IconType;
export declare const TbGlassCocktail: IconType;
export declare const TbGlassFull: IconType;
export declare const TbGlassGin: IconType;
export declare const TbGlassOff: IconType;
export declare const TbGlass: IconType;
export declare const TbGlobeOff: IconType;
export declare const TbGlobe: IconType;
export declare const TbGoGame: IconType;
export declare const TbGolfOff: IconType;
export declare const TbGolf: IconType;
export declare const TbGps: IconType;
export declare const TbGradienter: IconType;
export declare const TbGrain: IconType;
export declare const TbGraphOff: IconType;
export declare const TbGraph: IconType;
export declare const TbGrave2: IconType;
export declare const TbGrave: IconType;
export declare const TbGrid3X3: IconType;
export declare const TbGrid4X4: IconType;
export declare const TbGridDots: IconType;
export declare const TbGridGoldenratio: IconType;
export declare const TbGridPattern: IconType;
export declare const TbGridScan: IconType;
export declare const TbGrillFork: IconType;
export declare const TbGrillOff: IconType;
export declare const TbGrillSpatula: IconType;
export declare const TbGrill: IconType;
export declare const TbGripHorizontal: IconType;
export declare const TbGripVertical: IconType;
export declare const TbGrowth: IconType;
export declare const TbGuitarPick: IconType;
export declare const TbGymnastics: IconType;
export declare const TbH1: IconType;
export declare const TbH2: IconType;
export declare const TbH3: IconType;
export declare const TbH4: IconType;
export declare const TbH5: IconType;
export declare const TbH6: IconType;
export declare const TbHammerOff: IconType;
export declare const TbHammer: IconType;
export declare const TbHandClick: IconType;
export declare const TbHandFingerDown: IconType;
export declare const TbHandFingerLeft: IconType;
export declare const TbHandFingerOff: IconType;
export declare const TbHandFingerRight: IconType;
export declare const TbHandFinger: IconType;
export declare const TbHandGrab: IconType;
export declare const TbHandLittleFinger: IconType;
export declare const TbHandLoveYou: IconType;
export declare const TbHandMiddleFinger: IconType;
export declare const TbHandMove: IconType;
export declare const TbHandOff: IconType;
export declare const TbHandRingFinger: IconType;
export declare const TbHandSanitizer: IconType;
export declare const TbHandStop: IconType;
export declare const TbHandThreeFingers: IconType;
export declare const TbHandTwoFingers: IconType;
export declare const TbHanger2: IconType;
export declare const TbHangerOff: IconType;
export declare const TbHanger: IconType;
export declare const TbHash: IconType;
export declare const TbHazeMoon: IconType;
export declare const TbHaze: IconType;
export declare const TbHdr: IconType;
export declare const TbHeadingOff: IconType;
export declare const TbHeading: IconType;
export declare const TbHeadphonesOff: IconType;
export declare const TbHeadphones: IconType;
export declare const TbHeadsetOff: IconType;
export declare const TbHeadset: IconType;
export declare const TbHealthRecognition: IconType;
export declare const TbHeartBitcoin: IconType;
export declare const TbHeartBolt: IconType;
export declare const TbHeartBroken: IconType;
export declare const TbHeartCancel: IconType;
export declare const TbHeartCheck: IconType;
export declare const TbHeartCode: IconType;
export declare const TbHeartCog: IconType;
export declare const TbHeartDiscount: IconType;
export declare const TbHeartDollar: IconType;
export declare const TbHeartDown: IconType;
export declare const TbHeartExclamation: IconType;
export declare const TbHeartHandshake: IconType;
export declare const TbHeartMinus: IconType;
export declare const TbHeartOff: IconType;
export declare const TbHeartPause: IconType;
export declare const TbHeartPin: IconType;
export declare const TbHeartPlus: IconType;
export declare const TbHeartQuestion: IconType;
export declare const TbHeartRateMonitor: IconType;
export declare const TbHeartSearch: IconType;
export declare const TbHeartShare: IconType;
export declare const TbHeartSpark: IconType;
export declare const TbHeartStar: IconType;
export declare const TbHeartUp: IconType;
export declare const TbHeartX: IconType;
export declare const TbHeart: IconType;
export declare const TbHeartbeat: IconType;
export declare const TbHeartsOff: IconType;
export declare const TbHearts: IconType;
export declare const TbHelicopterLanding: IconType;
export declare const TbHelicopter: IconType;
export declare const TbHelmetOff: IconType;
export declare const TbHelmet: IconType;
export declare const TbHelpCircle: IconType;
export declare const TbHelpHexagon: IconType;
export declare const TbHelpOctagon: IconType;
export declare const TbHelpOff: IconType;
export declare const TbHelpSmall: IconType;
export declare const TbHelpSquareRounded: IconType;
export declare const TbHelpSquare: IconType;
export declare const TbHelpTriangle: IconType;
export declare const TbHelp: IconType;
export declare const TbHemisphereOff: IconType;
export declare const TbHemispherePlus: IconType;
export declare const TbHemisphere: IconType;
export declare const TbHexagon3D: IconType;
export declare const TbHexagonLetterA: IconType;
export declare const TbHexagonLetterB: IconType;
export declare const TbHexagonLetterC: IconType;
export declare const TbHexagonLetterD: IconType;
export declare const TbHexagonLetterE: IconType;
export declare const TbHexagonLetterF: IconType;
export declare const TbHexagonLetterG: IconType;
export declare const TbHexagonLetterH: IconType;
export declare const TbHexagonLetterI: IconType;
export declare const TbHexagonLetterJ: IconType;
export declare const TbHexagonLetterK: IconType;
export declare const TbHexagonLetterL: IconType;
export declare const TbHexagonLetterM: IconType;
export declare const TbHexagonLetterN: IconType;
export declare const TbHexagonLetterO: IconType;
export declare const TbHexagonLetterP: IconType;
export declare const TbHexagonLetterQ: IconType;
export declare const TbHexagonLetterR: IconType;
export declare const TbHexagonLetterS: IconType;
export declare const TbHexagonLetterT: IconType;
export declare const TbHexagonLetterU: IconType;
export declare const TbHexagonLetterV: IconType;
export declare const TbHexagonLetterW: IconType;
export declare const TbHexagonLetterX: IconType;
export declare const TbHexagonLetterY: IconType;
export declare const TbHexagonLetterZ: IconType;
export declare const TbHexagonMinus2: IconType;
export declare const TbHexagonMinus: IconType;
export declare const TbHexagonNumber0: IconType;
export declare const TbHexagonNumber1: IconType;
export declare const TbHexagonNumber2: IconType;
export declare const TbHexagonNumber3: IconType;
export declare const TbHexagonNumber4: IconType;
export declare const TbHexagonNumber5: IconType;
export declare const TbHexagonNumber6: IconType;
export declare const TbHexagonNumber7: IconType;
export declare const TbHexagonNumber8: IconType;
export declare const TbHexagonNumber9: IconType;
export declare const TbHexagonOff: IconType;
export declare const TbHexagonPlus2: IconType;
export declare const TbHexagonPlus: IconType;
export declare const TbHexagon: IconType;
export declare const TbHexagonalPrismOff: IconType;
export declare const TbHexagonalPrismPlus: IconType;
export declare const TbHexagonalPrism: IconType;
export declare const TbHexagonalPyramidOff: IconType;
export declare const TbHexagonalPyramidPlus: IconType;
export declare const TbHexagonalPyramid: IconType;
export declare const TbHexagonsOff: IconType;
export declare const TbHexagons: IconType;
export declare const TbHierarchy2: IconType;
export declare const TbHierarchy3: IconType;
export declare const TbHierarchyOff: IconType;
export declare const TbHierarchy: IconType;
export declare const TbHighlightOff: IconType;
export declare const TbHighlight: IconType;
export declare const TbHistoryOff: IconType;
export declare const TbHistoryToggle: IconType;
export declare const TbHistory: IconType;
export declare const TbHome2: IconType;
export declare const TbHomeBitcoin: IconType;
export declare const TbHomeBolt: IconType;
export declare const TbHomeCancel: IconType;
export declare const TbHomeCheck: IconType;
export declare const TbHomeCog: IconType;
export declare const TbHomeDollar: IconType;
export declare const TbHomeDot: IconType;
export declare const TbHomeDown: IconType;
export declare const TbHomeEco: IconType;
export declare const TbHomeEdit: IconType;
export declare const TbHomeExclamation: IconType;
export declare const TbHomeHand: IconType;
export declare const TbHomeHeart: IconType;
export declare const TbHomeInfinity: IconType;
export declare const TbHomeLink: IconType;
export declare const TbHomeMinus: IconType;
export declare const TbHomeMove: IconType;
export declare const TbHomeOff: IconType;
export declare const TbHomePlus: IconType;
export declare const TbHomeQuestion: IconType;
export declare const TbHomeRibbon: IconType;
export declare const TbHomeSearch: IconType;
export declare const TbHomeShare: IconType;
export declare const TbHomeShield: IconType;
export declare const TbHomeSignal: IconType;
export declare const TbHomeSpark: IconType;
export declare const TbHomeStar: IconType;
export declare const TbHomeStats: IconType;
export declare const TbHomeUp: IconType;
export declare const TbHomeX: IconType;
export declare const TbHome: IconType;
export declare const TbHorseToy: IconType;
export declare const TbHorse: IconType;
export declare const TbHorseshoe: IconType;
export declare const TbHospitalCircle: IconType;
export declare const TbHospital: IconType;
export declare const TbHotelService: IconType;
export declare const TbHourglassEmpty: IconType;
export declare const TbHourglassHigh: IconType;
export declare const TbHourglassLow: IconType;
export declare const TbHourglassOff: IconType;
export declare const TbHourglass: IconType;
export declare const TbHours12: IconType;
export declare const TbHours24: IconType;
export declare const TbHtml: IconType;
export declare const TbHttpConnect: IconType;
export declare const TbHttpDelete: IconType;
export declare const TbHttpGet: IconType;
export declare const TbHttpHead: IconType;
export declare const TbHttpOptions: IconType;
export declare const TbHttpPatch: IconType;
export declare const TbHttpPost: IconType;
export declare const TbHttpPut: IconType;
export declare const TbHttpQue: IconType;
export declare const TbHttpTrace: IconType;
export declare const TbIceCream2: IconType;
export declare const TbIceCreamOff: IconType;
export declare const TbIceCream: IconType;
export declare const TbIceSkating: IconType;
export declare const TbIconsOff: IconType;
export declare const TbIcons: IconType;
export declare const TbIdBadge2: IconType;
export declare const TbIdBadgeOff: IconType;
export declare const TbIdBadge: IconType;
export declare const TbIdOff: IconType;
export declare const TbId: IconType;
export declare const TbIkosaedr: IconType;
export declare const TbImageInPicture: IconType;
export declare const TbInboxOff: IconType;
export declare const TbInbox: IconType;
export declare const TbIndentDecrease: IconType;
export declare const TbIndentIncrease: IconType;
export declare const TbInfinityOff: IconType;
export declare const TbInfinity: IconType;
export declare const TbInfoCircle: IconType;
export declare const TbInfoHexagon: IconType;
export declare const TbInfoOctagon: IconType;
export declare const TbInfoSmall: IconType;
export declare const TbInfoSquareRounded: IconType;
export declare const TbInfoSquare: IconType;
export declare const TbInfoTriangle: IconType;
export declare const TbInnerShadowBottomLeft: IconType;
export declare const TbInnerShadowBottomRight: IconType;
export declare const TbInnerShadowBottom: IconType;
export declare const TbInnerShadowLeft: IconType;
export declare const TbInnerShadowRight: IconType;
export declare const TbInnerShadowTopLeft: IconType;
export declare const TbInnerShadowTopRight: IconType;
export declare const TbInnerShadowTop: IconType;
export declare const TbInputAi: IconType;
export declare const TbInputCheck: IconType;
export declare const TbInputSearch: IconType;
export declare const TbInputSpark: IconType;
export declare const TbInputX: IconType;
export declare const TbInvoice: IconType;
export declare const TbIroning1: IconType;
export declare const TbIroning2: IconType;
export declare const TbIroning3: IconType;
export declare const TbIroningOff: IconType;
export declare const TbIroningSteamOff: IconType;
export declare const TbIroningSteam: IconType;
export declare const TbIroning: IconType;
export declare const ************************: IconType;
export declare const TbIrregularPolyhedronPlus: IconType;
export declare const TbIrregularPolyhedron: IconType;
export declare const TbItalic: IconType;
export declare const TbJacket: IconType;
export declare const TbJetpack: IconType;
export declare const TbJewishStar: IconType;
export declare const TbJoinBevel: IconType;
export declare const TbJoinRound: IconType;
export declare const TbJoinStraight: IconType;
export declare const TbJoker: IconType;
export declare const TbJpg: IconType;
export declare const TbJson: IconType;
export declare const TbJumpRope: IconType;
export declare const TbKarate: IconType;
export declare const TbKayak: IconType;
export declare const TbKerning: IconType;
export declare const TbKeyOff: IconType;
export declare const TbKey: IconType;
export declare const TbKeyboardHide: IconType;
export declare const TbKeyboardOff: IconType;
export declare const TbKeyboardShow: IconType;
export declare const TbKeyboard: IconType;
export declare const TbKeyframeAlignCenter: IconType;
export declare const TbKeyframeAlignHorizontal: IconType;
export declare const TbKeyframeAlignVertical: IconType;
export declare const TbKeyframe: IconType;
export declare const TbKeyframes: IconType;
export declare const TbLabelImportant: IconType;
export declare const TbLabelOff: IconType;
export declare const TbLabel: IconType;
export declare const TbLadderOff: IconType;
export declare const TbLadder: IconType;
export declare const TbLadle: IconType;
export declare const TbLambda: IconType;
export declare const TbLamp2: IconType;
export declare const TbLampOff: IconType;
export declare const TbLamp: IconType;
export declare const TbLane: IconType;
export declare const TbLanguageHiragana: IconType;
export declare const TbLanguageKatakana: IconType;
export declare const TbLanguageOff: IconType;
export declare const TbLanguage: IconType;
export declare const TbLassoOff: IconType;
export declare const TbLassoPolygon: IconType;
export declare const TbLasso: IconType;
export declare const TbLaurelWreath1: IconType;
export declare const TbLaurelWreath2: IconType;
export declare const TbLaurelWreath3: IconType;
export declare const TbLaurelWreath: IconType;
export declare const TbLayersDifference: IconType;
export declare const TbLayersIntersect2: IconType;
export declare const TbLayersIntersect: IconType;
export declare const TbLayersLinked: IconType;
export declare const TbLayersOff: IconType;
export declare const TbLayersSelectedBottom: IconType;
export declare const TbLayersSelected: IconType;
export declare const TbLayersSubtract: IconType;
export declare const TbLayersUnion: IconType;
export declare const TbLayout2: IconType;
export declare const TbLayoutAlignBottom: IconType;
export declare const TbLayoutAlignCenter: IconType;
export declare const TbLayoutAlignLeft: IconType;
export declare const TbLayoutAlignMiddle: IconType;
export declare const TbLayoutAlignRight: IconType;
export declare const TbLayoutAlignTop: IconType;
export declare const TbLayoutBoardSplit: IconType;
export declare const TbLayoutBoard: IconType;
export declare const TbLayoutBottombarCollapse: IconType;
export declare const TbLayoutBottombarExpand: IconType;
export declare const TbLayoutBottombarInactive: IconType;
export declare const TbLayoutBottombar: IconType;
export declare const TbLayoutCards: IconType;
export declare const TbLayoutCollage: IconType;
export declare const TbLayoutColumns: IconType;
export declare const TbLayoutDashboard: IconType;
export declare const TbLayoutDistributeHorizontal: IconType;
export declare const TbLayoutDistributeVertical: IconType;
export declare const TbLayoutGridAdd: IconType;
export declare const TbLayoutGridRemove: IconType;
export declare const TbLayoutGrid: IconType;
export declare const TbLayoutKanban: IconType;
export declare const TbLayoutList: IconType;
export declare const TbLayoutNavbarCollapse: IconType;
export declare const TbLayoutNavbarExpand: IconType;
export declare const TbLayoutNavbarInactive: IconType;
export declare const TbLayoutNavbar: IconType;
export declare const TbLayoutOff: IconType;
export declare const TbLayoutRows: IconType;
export declare const TbLayoutSidebarInactive: IconType;
export declare const TbLayoutSidebarLeftCollapse: IconType;
export declare const TbLayoutSidebarLeftExpand: IconType;
export declare const TbLayoutSidebarRightCollapse: IconType;
export declare const TbLayoutSidebarRightExpand: IconType;
export declare const TbLayoutSidebarRightInactive: IconType;
export declare const TbLayoutSidebarRight: IconType;
export declare const TbLayoutSidebar: IconType;
export declare const TbLayout: IconType;
export declare const TbLeaf2: IconType;
export declare const TbLeafOff: IconType;
export declare const TbLeaf: IconType;
export declare const TbLegoOff: IconType;
export declare const TbLego: IconType;
export declare const TbLemon2: IconType;
export declare const TbLemon: IconType;
export declare const TbLetterASmall: IconType;
export declare const TbLetterA: IconType;
export declare const TbLetterBSmall: IconType;
export declare const TbLetterB: IconType;
export declare const TbLetterCSmall: IconType;
export declare const TbLetterC: IconType;
export declare const TbLetterCaseLower: IconType;
export declare const TbLetterCaseToggle: IconType;
export declare const TbLetterCaseUpper: IconType;
export declare const TbLetterCase: IconType;
export declare const TbLetterDSmall: IconType;
export declare const TbLetterD: IconType;
export declare const TbLetterESmall: IconType;
export declare const TbLetterE: IconType;
export declare const TbLetterFSmall: IconType;
export declare const TbLetterF: IconType;
export declare const TbLetterGSmall: IconType;
export declare const TbLetterG: IconType;
export declare const TbLetterHSmall: IconType;
export declare const TbLetterH: IconType;
export declare const TbLetterISmall: IconType;
export declare const TbLetterI: IconType;
export declare const TbLetterJSmall: IconType;
export declare const TbLetterJ: IconType;
export declare const TbLetterKSmall: IconType;
export declare const TbLetterK: IconType;
export declare const TbLetterLSmall: IconType;
export declare const TbLetterL: IconType;
export declare const TbLetterMSmall: IconType;
export declare const TbLetterM: IconType;
export declare const TbLetterNSmall: IconType;
export declare const TbLetterN: IconType;
export declare const TbLetterOSmall: IconType;
export declare const TbLetterO: IconType;
export declare const TbLetterPSmall: IconType;
export declare const TbLetterP: IconType;
export declare const TbLetterQSmall: IconType;
export declare const TbLetterQ: IconType;
export declare const TbLetterRSmall: IconType;
export declare const TbLetterR: IconType;
export declare const TbLetterSSmall: IconType;
export declare const TbLetterS: IconType;
export declare const TbLetterSpacing: IconType;
export declare const TbLetterTSmall: IconType;
export declare const TbLetterT: IconType;
export declare const TbLetterUSmall: IconType;
export declare const TbLetterU: IconType;
export declare const TbLetterVSmall: IconType;
export declare const TbLetterV: IconType;
export declare const TbLetterWSmall: IconType;
export declare const TbLetterW: IconType;
export declare const TbLetterXSmall: IconType;
export declare const TbLetterX: IconType;
export declare const TbLetterYSmall: IconType;
export declare const TbLetterY: IconType;
export declare const TbLetterZSmall: IconType;
export declare const TbLetterZ: IconType;
export declare const TbLibraryMinus: IconType;
export declare const TbLibraryPhoto: IconType;
export declare const TbLibraryPlus: IconType;
export declare const TbLibrary: IconType;
export declare const TbLicenseOff: IconType;
export declare const TbLicense: IconType;
export declare const TbLifebuoyOff: IconType;
export declare const TbLifebuoy: IconType;
export declare const TbLighter: IconType;
export declare const TbLineDashed: IconType;
export declare const TbLineDotted: IconType;
export declare const TbLineHeight: IconType;
export declare const TbLineScan: IconType;
export declare const TbLine: IconType;
export declare const TbLinkMinus: IconType;
export declare const TbLinkOff: IconType;
export declare const TbLinkPlus: IconType;
export declare const TbLink: IconType;
export declare const TbListCheck: IconType;
export declare const TbListDetails: IconType;
export declare const TbListLetters: IconType;
export declare const TbListNumbers: IconType;
export declare const TbListSearch: IconType;
export declare const TbListTree: IconType;
export declare const TbList: IconType;
export declare const TbLivePhotoOff: IconType;
export declare const TbLivePhoto: IconType;
export declare const TbLiveView: IconType;
export declare const TbLoadBalancer: IconType;
export declare const TbLoader2: IconType;
export declare const TbLoader3: IconType;
export declare const TbLoaderQuarter: IconType;
export declare const TbLoader: IconType;
export declare const TbLocationBolt: IconType;
export declare const TbLocationBroken: IconType;
export declare const TbLocationCancel: IconType;
export declare const TbLocationCheck: IconType;
export declare const TbLocationCode: IconType;
export declare const TbLocationCog: IconType;
export declare const TbLocationDiscount: IconType;
export declare const TbLocationDollar: IconType;
export declare const TbLocationDown: IconType;
export declare const TbLocationExclamation: IconType;
export declare const TbLocationHeart: IconType;
export declare const TbLocationMinus: IconType;
export declare const TbLocationOff: IconType;
export declare const TbLocationPause: IconType;
export declare const TbLocationPin: IconType;
export declare const TbLocationPlus: IconType;
export declare const TbLocationQuestion: IconType;
export declare const TbLocationSearch: IconType;
export declare const TbLocationShare: IconType;
export declare const TbLocationStar: IconType;
export declare const TbLocationUp: IconType;
export declare const TbLocationX: IconType;
export declare const TbLocation: IconType;
export declare const TbLockAccessOff: IconType;
export declare const TbLockAccess: IconType;
export declare const TbLockBitcoin: IconType;
export declare const TbLockBolt: IconType;
export declare const TbLockCancel: IconType;
export declare const TbLockCheck: IconType;
export declare const TbLockCode: IconType;
export declare const TbLockCog: IconType;
export declare const TbLockDollar: IconType;
export declare const TbLockDown: IconType;
export declare const TbLockExclamation: IconType;
export declare const TbLockHeart: IconType;
export declare const TbLockMinus: IconType;
export declare const TbLockOff: IconType;
export declare const TbLockOpen2: IconType;
export declare const TbLockOpenOff: IconType;
export declare const TbLockOpen: IconType;
export declare const TbLockPassword: IconType;
export declare const TbLockPause: IconType;
export declare const TbLockPin: IconType;
export declare const TbLockPlus: IconType;
export declare const TbLockQuestion: IconType;
export declare const TbLockSearch: IconType;
export declare const TbLockShare: IconType;
export declare const TbLockSquareRounded: IconType;
export declare const TbLockSquare: IconType;
export declare const TbLockStar: IconType;
export declare const TbLockUp: IconType;
export declare const TbLockX: IconType;
export declare const TbLock: IconType;
export declare const TbLogicAnd: IconType;
export declare const TbLogicBuffer: IconType;
export declare const TbLogicNand: IconType;
export declare const TbLogicNor: IconType;
export declare const TbLogicNot: IconType;
export declare const TbLogicOr: IconType;
export declare const TbLogicXnor: IconType;
export declare const TbLogicXor: IconType;
export declare const TbLogin2: IconType;
export declare const TbLogin: IconType;
export declare const TbLogout2: IconType;
export declare const TbLogout: IconType;
export declare const TbLogs: IconType;
export declare const TbLollipopOff: IconType;
export declare const TbLollipop: IconType;
export declare const TbLuggageOff: IconType;
export declare const TbLuggage: IconType;
export declare const TbLungsOff: IconType;
export declare const TbLungs: IconType;
export declare const TbMacroOff: IconType;
export declare const TbMacro: IconType;
export declare const TbMagnetOff: IconType;
export declare const TbMagnet: IconType;
export declare const TbMagnetic: IconType;
export declare const TbMailAi: IconType;
export declare const TbMailBitcoin: IconType;
export declare const TbMailBolt: IconType;
export declare const TbMailCancel: IconType;
export declare const TbMailCheck: IconType;
export declare const TbMailCode: IconType;
export declare const TbMailCog: IconType;
export declare const TbMailDollar: IconType;
export declare const TbMailDown: IconType;
export declare const TbMailExclamation: IconType;
export declare const TbMailFast: IconType;
export declare const TbMailForward: IconType;
export declare const TbMailHeart: IconType;
export declare const TbMailMinus: IconType;
export declare const TbMailOff: IconType;
export declare const TbMailOpened: IconType;
export declare const TbMailPause: IconType;
export declare const TbMailPin: IconType;
export declare const TbMailPlus: IconType;
export declare const TbMailQuestion: IconType;
export declare const TbMailSearch: IconType;
export declare const TbMailShare: IconType;
export declare const TbMailSpark: IconType;
export declare const TbMailStar: IconType;
export declare const TbMailUp: IconType;
export declare const TbMailX: IconType;
export declare const TbMail: IconType;
export declare const TbMailboxOff: IconType;
export declare const TbMailbox: IconType;
export declare const TbMan: IconType;
export declare const TbManualGearbox: IconType;
export declare const TbMap2: IconType;
export declare const TbMapBolt: IconType;
export declare const TbMapCancel: IconType;
export declare const TbMapCheck: IconType;
export declare const TbMapCode: IconType;
export declare const TbMapCog: IconType;
export declare const TbMapDiscount: IconType;
export declare const TbMapDollar: IconType;
export declare const TbMapDown: IconType;
export declare const TbMapEast: IconType;
export declare const TbMapExclamation: IconType;
export declare const TbMapHeart: IconType;
export declare const TbMapMinus: IconType;
export declare const TbMapNorth: IconType;
export declare const TbMapOff: IconType;
export declare const TbMapPause: IconType;
export declare const TbMapPin2: IconType;
export declare const TbMapPinBolt: IconType;
export declare const TbMapPinCancel: IconType;
export declare const TbMapPinCheck: IconType;
export declare const TbMapPinCode: IconType;
export declare const TbMapPinCog: IconType;
export declare const TbMapPinDollar: IconType;
export declare const TbMapPinDown: IconType;
export declare const TbMapPinExclamation: IconType;
export declare const TbMapPinHeart: IconType;
export declare const TbMapPinMinus: IconType;
export declare const TbMapPinOff: IconType;
export declare const TbMapPinPause: IconType;
export declare const TbMapPinPin: IconType;
export declare const TbMapPinPlus: IconType;
export declare const TbMapPinQuestion: IconType;
export declare const TbMapPinSearch: IconType;
export declare const TbMapPinShare: IconType;
export declare const TbMapPinStar: IconType;
export declare const TbMapPinUp: IconType;
export declare const TbMapPinX: IconType;
export declare const TbMapPin: IconType;
export declare const TbMapPins: IconType;
export declare const TbMapPlus: IconType;
export declare const TbMapQuestion: IconType;
export declare const TbMapRoute: IconType;
export declare const TbMapSearch: IconType;
export declare const TbMapShare: IconType;
export declare const TbMapSouth: IconType;
export declare const TbMapStar: IconType;
export declare const TbMapUp: IconType;
export declare const TbMapWest: IconType;
export declare const TbMapX: IconType;
export declare const TbMap: IconType;
export declare const TbMarkdownOff: IconType;
export declare const TbMarkdown: IconType;
export declare const TbMarquee2: IconType;
export declare const TbMarqueeOff: IconType;
export declare const TbMarquee: IconType;
export declare const TbMars: IconType;
export declare const TbMaskOff: IconType;
export declare const TbMask: IconType;
export declare const TbMasksTheaterOff: IconType;
export declare const TbMasksTheater: IconType;
export declare const TbMassage: IconType;
export declare const TbMatchstick: IconType;
export declare const TbMath1Divide2: IconType;
export declare const TbMath1Divide3: IconType;
export declare const TbMathAvg: IconType;
export declare const TbMathCos: IconType;
export declare const TbMathCtg: IconType;
export declare const TbMathEqualGreater: IconType;
export declare const TbMathEqualLower: IconType;
export declare const TbMathFunctionOff: IconType;
export declare const TbMathFunctionY: IconType;
export declare const TbMathFunction: IconType;
export declare const TbMathGreater: IconType;
export declare const TbMathIntegralX: IconType;
export declare const TbMathIntegral: IconType;
export declare const TbMathIntegrals: IconType;
export declare const TbMathLower: IconType;
export declare const TbMathMaxMin: IconType;
export declare const TbMathMax: IconType;
export declare const TbMathMin: IconType;
export declare const TbMathNot: IconType;
export declare const TbMathOff: IconType;
export declare const TbMathPiDivide2: IconType;
export declare const TbMathPi: IconType;
export declare const TbMathSec: IconType;
export declare const TbMathSin: IconType;
export declare const TbMathSymbols: IconType;
export declare const TbMathTg: IconType;
export declare const TbMathXDivide2: IconType;
export declare const TbMathXDivideY2: IconType;
export declare const TbMathXDivideY: IconType;
export declare const TbMathXFloorDivideY: IconType;
export declare const TbMathXMinusX: IconType;
export declare const TbMathXMinusY: IconType;
export declare const TbMathXPlusX: IconType;
export declare const TbMathXPlusY: IconType;
export declare const TbMathXy: IconType;
export declare const TbMathYMinusY: IconType;
export declare const TbMathYPlusY: IconType;
export declare const TbMath: IconType;
export declare const TbMatrix: IconType;
export declare const TbMaximizeOff: IconType;
export declare const TbMaximize: IconType;
export declare const TbMeatOff: IconType;
export declare const TbMeat: IconType;
export declare const TbMedal2: IconType;
export declare const TbMedal: IconType;
export declare const TbMedicalCrossCircle: IconType;
export declare const TbMedicalCrossOff: IconType;
export declare const TbMedicalCross: IconType;
export declare const TbMedicineSyrup: IconType;
export declare const TbMeeple: IconType;
export declare const TbMelon: IconType;
export declare const TbMenorah: IconType;
export declare const TbMenu2: IconType;
export declare const TbMenu3: IconType;
export declare const TbMenu4: IconType;
export declare const TbMenuDeep: IconType;
export declare const TbMenuOrder: IconType;
export declare const TbMenu: IconType;
export declare const TbMessage2Bolt: IconType;
export declare const TbMessage2Cancel: IconType;
export declare const TbMessage2Check: IconType;
export declare const TbMessage2Code: IconType;
export declare const TbMessage2Cog: IconType;
export declare const TbMessage2Dollar: IconType;
export declare const TbMessage2Down: IconType;
export declare const TbMessage2Exclamation: IconType;
export declare const TbMessage2Heart: IconType;
export declare const TbMessage2Minus: IconType;
export declare const TbMessage2Off: IconType;
export declare const TbMessage2Pause: IconType;
export declare const TbMessage2Pin: IconType;
export declare const TbMessage2Plus: IconType;
export declare const TbMessage2Question: IconType;
export declare const TbMessage2Search: IconType;
export declare const TbMessage2Share: IconType;
export declare const TbMessage2Star: IconType;
export declare const TbMessage2Up: IconType;
export declare const TbMessage2X: IconType;
export declare const TbMessage2: IconType;
export declare const TbMessageBolt: IconType;
export declare const TbMessageCancel: IconType;
export declare const TbMessageChatbot: IconType;
export declare const TbMessageCheck: IconType;
export declare const TbMessageCircleBolt: IconType;
export declare const TbMessageCircleCancel: IconType;
export declare const TbMessageCircleCheck: IconType;
export declare const TbMessageCircleCode: IconType;
export declare const TbMessageCircleCog: IconType;
export declare const TbMessageCircleDollar: IconType;
export declare const TbMessageCircleDown: IconType;
export declare const TbMessageCircleExclamation: IconType;
export declare const TbMessageCircleHeart: IconType;
export declare const TbMessageCircleMinus: IconType;
export declare const TbMessageCircleOff: IconType;
export declare const TbMessageCirclePause: IconType;
export declare const TbMessageCirclePin: IconType;
export declare const TbMessageCirclePlus: IconType;
export declare const TbMessageCircleQuestion: IconType;
export declare const TbMessageCircleSearch: IconType;
export declare const TbMessageCircleShare: IconType;
export declare const TbMessageCircleStar: IconType;
export declare const TbMessageCircleUp: IconType;
export declare const TbMessageCircleUser: IconType;
export declare const TbMessageCircleX: IconType;
export declare const TbMessageCircle: IconType;
export declare const TbMessageCode: IconType;
export declare const TbMessageCog: IconType;
export declare const TbMessageDollar: IconType;
export declare const TbMessageDots: IconType;
export declare const TbMessageDown: IconType;
export declare const TbMessageExclamation: IconType;
export declare const TbMessageForward: IconType;
export declare const TbMessageHeart: IconType;
export declare const TbMessageLanguage: IconType;
export declare const TbMessageMinus: IconType;
export declare const TbMessageOff: IconType;
export declare const TbMessagePause: IconType;
export declare const TbMessagePin: IconType;
export declare const TbMessagePlus: IconType;
export declare const TbMessageQuestion: IconType;
export declare const TbMessageReply: IconType;
export declare const TbMessageReport: IconType;
export declare const TbMessageSearch: IconType;
export declare const TbMessageShare: IconType;
export declare const TbMessageStar: IconType;
export declare const TbMessageUp: IconType;
export declare const TbMessageUser: IconType;
export declare const TbMessageX: IconType;
export declare const TbMessage: IconType;
export declare const TbMessagesOff: IconType;
export declare const TbMessages: IconType;
export declare const TbMeteorOff: IconType;
export declare const TbMeteor: IconType;
export declare const TbMeterCube: IconType;
export declare const TbMeterSquare: IconType;
export declare const TbMetronome: IconType;
export declare const TbMichelinBibGourmand: IconType;
export declare const TbMichelinStarGreen: IconType;
export declare const TbMichelinStar: IconType;
export declare const TbMickey: IconType;
export declare const TbMicrophone2Off: IconType;
export declare const TbMicrophone2: IconType;
export declare const TbMicrophoneOff: IconType;
export declare const TbMicrophone: IconType;
export declare const TbMicroscopeOff: IconType;
export declare const TbMicroscope: IconType;
export declare const TbMicrowaveOff: IconType;
export declare const TbMicrowave: IconType;
export declare const TbMilitaryAward: IconType;
export declare const TbMilitaryRank: IconType;
export declare const TbMilkOff: IconType;
export declare const TbMilk: IconType;
export declare const TbMilkshake: IconType;
export declare const TbMinimize: IconType;
export declare const TbMinusVertical: IconType;
export declare const TbMinus: IconType;
export declare const TbMistOff: IconType;
export declare const TbMist: IconType;
export declare const TbMobiledataOff: IconType;
export declare const TbMobiledata: IconType;
export declare const TbMoneybag: IconType;
export declare const TbMonkeybar: IconType;
export declare const TbMoodAngry: IconType;
export declare const TbMoodAnnoyed2: IconType;
export declare const TbMoodAnnoyed: IconType;
export declare const TbMoodBitcoin: IconType;
export declare const TbMoodBoy: IconType;
export declare const TbMoodCheck: IconType;
export declare const TbMoodCog: IconType;
export declare const TbMoodConfuzed: IconType;
export declare const TbMoodCrazyHappy: IconType;
export declare const TbMoodCry: IconType;
export declare const TbMoodDollar: IconType;
export declare const TbMoodEdit: IconType;
export declare const TbMoodEmpty: IconType;
export declare const TbMoodHappy: IconType;
export declare const TbMoodHeart: IconType;
export declare const TbMoodKid: IconType;
export declare const TbMoodLookDown: IconType;
export declare const TbMoodLookLeft: IconType;
export declare const TbMoodLookRight: IconType;
export declare const TbMoodLookUp: IconType;
export declare const TbMoodMinus: IconType;
export declare const TbMoodNerd: IconType;
export declare const TbMoodNervous: IconType;
export declare const TbMoodNeutral: IconType;
export declare const TbMoodOff: IconType;
export declare const TbMoodPin: IconType;
export declare const TbMoodPlus: IconType;
export declare const TbMoodPuzzled: IconType;
export declare const TbMoodSad2: IconType;
export declare const TbMoodSadDizzy: IconType;
export declare const TbMoodSadSquint: IconType;
export declare const TbMoodSad: IconType;
export declare const TbMoodSearch: IconType;
export declare const TbMoodShare: IconType;
export declare const TbMoodSick: IconType;
export declare const TbMoodSilence: IconType;
export declare const TbMoodSing: IconType;
export declare const TbMoodSmileBeam: IconType;
export declare const TbMoodSmileDizzy: IconType;
export declare const TbMoodSmile: IconType;
export declare const TbMoodSpark: IconType;
export declare const TbMoodSurprised: IconType;
export declare const TbMoodTongueWink2: IconType;
export declare const TbMoodTongueWink: IconType;
export declare const TbMoodTongue: IconType;
export declare const TbMoodUnamused: IconType;
export declare const TbMoodUp: IconType;
export declare const TbMoodWink2: IconType;
export declare const TbMoodWink: IconType;
export declare const TbMoodWrrr: IconType;
export declare const TbMoodX: IconType;
export declare const TbMoodXd: IconType;
export declare const TbMoon2: IconType;
export declare const TbMoonOff: IconType;
export declare const TbMoonStars: IconType;
export declare const TbMoon: IconType;
export declare const TbMoped: IconType;
export declare const TbMotorbike: IconType;
export declare const TbMountainOff: IconType;
export declare const TbMountain: IconType;
export declare const TbMouse2: IconType;
export declare const TbMouseOff: IconType;
export declare const TbMouse: IconType;
export declare const TbMoustache: IconType;
export declare const TbMovieOff: IconType;
export declare const TbMovie: IconType;
export declare const TbMugOff: IconType;
export declare const TbMug: IconType;
export declare const TbMultiplier05X: IconType;
export declare const TbMultiplier15X: IconType;
export declare const TbMultiplier1X: IconType;
export declare const TbMultiplier2X: IconType;
export declare const TbMushroomOff: IconType;
export declare const TbMushroom: IconType;
export declare const TbMusicBolt: IconType;
export declare const TbMusicCancel: IconType;
export declare const TbMusicCheck: IconType;
export declare const TbMusicCode: IconType;
export declare const TbMusicCog: IconType;
export declare const TbMusicDiscount: IconType;
export declare const TbMusicDollar: IconType;
export declare const TbMusicDown: IconType;
export declare const TbMusicExclamation: IconType;
export declare const TbMusicHeart: IconType;
export declare const TbMusicMinus: IconType;
export declare const TbMusicOff: IconType;
export declare const TbMusicPause: IconType;
export declare const TbMusicPin: IconType;
export declare const TbMusicPlus: IconType;
export declare const TbMusicQuestion: IconType;
export declare const TbMusicSearch: IconType;
export declare const TbMusicShare: IconType;
export declare const TbMusicStar: IconType;
export declare const TbMusicUp: IconType;
export declare const TbMusicX: IconType;
export declare const TbMusic: IconType;
export declare const TbNavigationBolt: IconType;
export declare const TbNavigationCancel: IconType;
export declare const TbNavigationCheck: IconType;
export declare const TbNavigationCode: IconType;
export declare const TbNavigationCog: IconType;
export declare const TbNavigationDiscount: IconType;
export declare const TbNavigationDollar: IconType;
export declare const TbNavigationDown: IconType;
export declare const TbNavigationEast: IconType;
export declare const TbNavigationExclamation: IconType;
export declare const TbNavigationHeart: IconType;
export declare const TbNavigationMinus: IconType;
export declare const TbNavigationNorth: IconType;
export declare const TbNavigationOff: IconType;
export declare const TbNavigationPause: IconType;
export declare const TbNavigationPin: IconType;
export declare const TbNavigationPlus: IconType;
export declare const TbNavigationQuestion: IconType;
export declare const TbNavigationSearch: IconType;
export declare const TbNavigationShare: IconType;
export declare const TbNavigationSouth: IconType;
export declare const TbNavigationStar: IconType;
export declare const TbNavigationTop: IconType;
export declare const TbNavigationUp: IconType;
export declare const TbNavigationWest: IconType;
export declare const TbNavigationX: IconType;
export declare const TbNavigation: IconType;
export declare const TbNeedleThread: IconType;
export declare const TbNeedle: IconType;
export declare const TbNetworkOff: IconType;
export declare const TbNetwork: IconType;
export declare const TbNewSection: IconType;
export declare const TbNewsOff: IconType;
export declare const TbNews: IconType;
export declare const TbNfcOff: IconType;
export declare const TbNfc: IconType;
export declare const TbNoCopyright: IconType;
export declare const TbNoCreativeCommons: IconType;
export declare const TbNoDerivatives: IconType;
export declare const TbNorthStar: IconType;
export declare const TbNoteOff: IconType;
export declare const TbNote: IconType;
export declare const TbNotebookOff: IconType;
export declare const TbNotebook: IconType;
export declare const TbNotesOff: IconType;
export declare const TbNotes: IconType;
export declare const TbNotificationOff: IconType;
export declare const TbNotification: IconType;
export declare const TbNumber0Small: IconType;
export declare const TbNumber0: IconType;
export declare const TbNumber1Small: IconType;
export declare const TbNumber1: IconType;
export declare const TbNumber10Small: IconType;
export declare const TbNumber10: IconType;
export declare const TbNumber100Small: IconType;
export declare const TbNumber11Small: IconType;
export declare const TbNumber11: IconType;
export declare const TbNumber12Small: IconType;
export declare const TbNumber123: IconType;
export declare const TbNumber13Small: IconType;
export declare const TbNumber14Small: IconType;
export declare const TbNumber15Small: IconType;
export declare const TbNumber16Small: IconType;
export declare const TbNumber17Small: IconType;
export declare const TbNumber18Small: IconType;
export declare const TbNumber19Small: IconType;
export declare const TbNumber2Small: IconType;
export declare const TbNumber2: IconType;
export declare const TbNumber20Small: IconType;
export declare const TbNumber21Small: IconType;
export declare const TbNumber22Small: IconType;
export declare const TbNumber23Small: IconType;
export declare const TbNumber24Small: IconType;
export declare const TbNumber25Small: IconType;
export declare const TbNumber26Small: IconType;
export declare const TbNumber27Small: IconType;
export declare const TbNumber28Small: IconType;
export declare const TbNumber29Small: IconType;
export declare const TbNumber3Small: IconType;
export declare const TbNumber3: IconType;
export declare const TbNumber30Small: IconType;
export declare const TbNumber31Small: IconType;
export declare const TbNumber32Small: IconType;
export declare const TbNumber33Small: IconType;
export declare const TbNumber34Small: IconType;
export declare const TbNumber35Small: IconType;
export declare const TbNumber36Small: IconType;
export declare const TbNumber37Small: IconType;
export declare const TbNumber38Small: IconType;
export declare const TbNumber39Small: IconType;
export declare const TbNumber4Small: IconType;
export declare const TbNumber4: IconType;
export declare const TbNumber40Small: IconType;
export declare const TbNumber41Small: IconType;
export declare const TbNumber42Small: IconType;
export declare const TbNumber43Small: IconType;
export declare const TbNumber44Small: IconType;
export declare const TbNumber45Small: IconType;
export declare const TbNumber46Small: IconType;
export declare const TbNumber47Small: IconType;
export declare const TbNumber48Small: IconType;
export declare const TbNumber49Small: IconType;
export declare const TbNumber5Small: IconType;
export declare const TbNumber5: IconType;
export declare const TbNumber50Small: IconType;
export declare const TbNumber51Small: IconType;
export declare const TbNumber52Small: IconType;
export declare const TbNumber53Small: IconType;
export declare const TbNumber54Small: IconType;
export declare const TbNumber55Small: IconType;
export declare const TbNumber56Small: IconType;
export declare const TbNumber57Small: IconType;
export declare const TbNumber58Small: IconType;
export declare const TbNumber59Small: IconType;
export declare const TbNumber6Small: IconType;
export declare const TbNumber6: IconType;
export declare const TbNumber60Small: IconType;
export declare const TbNumber61Small: IconType;
export declare const TbNumber62Small: IconType;
export declare const TbNumber63Small: IconType;
export declare const TbNumber64Small: IconType;
export declare const TbNumber65Small: IconType;
export declare const TbNumber66Small: IconType;
export declare const TbNumber67Small: IconType;
export declare const TbNumber68Small: IconType;
export declare const TbNumber69Small: IconType;
export declare const TbNumber7Small: IconType;
export declare const TbNumber7: IconType;
export declare const TbNumber70Small: IconType;
export declare const TbNumber71Small: IconType;
export declare const TbNumber72Small: IconType;
export declare const TbNumber73Small: IconType;
export declare const TbNumber74Small: IconType;
export declare const TbNumber75Small: IconType;
export declare const TbNumber76Small: IconType;
export declare const TbNumber77Small: IconType;
export declare const TbNumber78Small: IconType;
export declare const TbNumber79Small: IconType;
export declare const TbNumber8Small: IconType;
export declare const TbNumber8: IconType;
export declare const TbNumber80Small: IconType;
export declare const TbNumber81Small: IconType;
export declare const TbNumber82Small: IconType;
export declare const TbNumber83Small: IconType;
export declare const TbNumber84Small: IconType;
export declare const TbNumber85Small: IconType;
export declare const TbNumber86Small: IconType;
export declare const TbNumber87Small: IconType;
export declare const TbNumber88Small: IconType;
export declare const TbNumber89Small: IconType;
export declare const TbNumber9Small: IconType;
export declare const TbNumber9: IconType;
export declare const TbNumber90Small: IconType;
export declare const TbNumber91Small: IconType;
export declare const TbNumber92Small: IconType;
export declare const TbNumber93Small: IconType;
export declare const TbNumber94Small: IconType;
export declare const TbNumber95Small: IconType;
export declare const TbNumber96Small: IconType;
export declare const TbNumber97Small: IconType;
export declare const TbNumber98Small: IconType;
export declare const TbNumber99Small: IconType;
export declare const TbNumber: IconType;
export declare const TbNumbers: IconType;
export declare const TbNurse: IconType;
export declare const TbNut: IconType;
export declare const TbObjectScan: IconType;
export declare const TbOctagonMinus2: IconType;
export declare const TbOctagonMinus: IconType;
export declare const TbOctagonOff: IconType;
export declare const TbOctagonPlus2: IconType;
export declare const TbOctagonPlus: IconType;
export declare const TbOctagon: IconType;
export declare const TbOctahedronOff: IconType;
export declare const TbOctahedronPlus: IconType;
export declare const TbOctahedron: IconType;
export declare const TbOld: IconType;
export declare const TbOlympicsOff: IconType;
export declare const TbOlympics: IconType;
export declare const TbOm: IconType;
export declare const TbOmega: IconType;
export declare const TbOutbound: IconType;
export declare const TbOutlet: IconType;
export declare const TbOvalVertical: IconType;
export declare const TbOval: IconType;
export declare const TbOverline: IconType;
export declare const TbPackageExport: IconType;
export declare const TbPackageImport: IconType;
export declare const TbPackageOff: IconType;
export declare const TbPackage: IconType;
export declare const TbPackages: IconType;
export declare const TbPacman: IconType;
export declare const TbPageBreak: IconType;
export declare const TbPaintOff: IconType;
export declare const TbPaint: IconType;
export declare const TbPaletteOff: IconType;
export declare const TbPalette: IconType;
export declare const TbPanoramaHorizontalOff: IconType;
export declare const TbPanoramaHorizontal: IconType;
export declare const TbPanoramaVerticalOff: IconType;
export declare const TbPanoramaVertical: IconType;
export declare const TbPaperBagOff: IconType;
export declare const TbPaperBag: IconType;
export declare const TbPaperclip: IconType;
export declare const TbParachuteOff: IconType;
export declare const TbParachute: IconType;
export declare const TbParenthesesOff: IconType;
export declare const TbParentheses: IconType;
export declare const TbParkingCircle: IconType;
export declare const TbParkingOff: IconType;
export declare const TbParking: IconType;
export declare const TbPasswordFingerprint: IconType;
export declare const TbPasswordMobilePhone: IconType;
export declare const TbPasswordUser: IconType;
export declare const TbPassword: IconType;
export declare const TbPawOff: IconType;
export declare const TbPaw: IconType;
export declare const TbPaywall: IconType;
export declare const TbPdf: IconType;
export declare const TbPeace: IconType;
export declare const TbPencilBolt: IconType;
export declare const TbPencilCancel: IconType;
export declare const TbPencilCheck: IconType;
export declare const TbPencilCode: IconType;
export declare const TbPencilCog: IconType;
export declare const TbPencilDiscount: IconType;
export declare const TbPencilDollar: IconType;
export declare const TbPencilDown: IconType;
export declare const TbPencilExclamation: IconType;
export declare const TbPencilHeart: IconType;
export declare const TbPencilMinus: IconType;
export declare const TbPencilOff: IconType;
export declare const TbPencilPause: IconType;
export declare const TbPencilPin: IconType;
export declare const TbPencilPlus: IconType;
export declare const TbPencilQuestion: IconType;
export declare const TbPencilSearch: IconType;
export declare const TbPencilShare: IconType;
export declare const TbPencilStar: IconType;
export declare const TbPencilUp: IconType;
export declare const TbPencilX: IconType;
export declare const TbPencil: IconType;
export declare const TbPennant2: IconType;
export declare const TbPennantOff: IconType;
export declare const TbPennant: IconType;
export declare const TbPentagonMinus: IconType;
export declare const TbPentagonNumber0: IconType;
export declare const TbPentagonNumber1: IconType;
export declare const TbPentagonNumber2: IconType;
export declare const TbPentagonNumber3: IconType;
export declare const TbPentagonNumber4: IconType;
export declare const TbPentagonNumber5: IconType;
export declare const TbPentagonNumber6: IconType;
export declare const TbPentagonNumber7: IconType;
export declare const TbPentagonNumber8: IconType;
export declare const TbPentagonNumber9: IconType;
export declare const TbPentagonOff: IconType;
export declare const TbPentagonPlus: IconType;
export declare const TbPentagonX: IconType;
export declare const TbPentagon: IconType;
export declare const TbPentagram: IconType;
export declare const TbPepperOff: IconType;
export declare const TbPepper: IconType;
export declare const TbPercentage0: IconType;
export declare const TbPercentage10: IconType;
export declare const TbPercentage100: IconType;
export declare const TbPercentage20: IconType;
export declare const TbPercentage25: IconType;
export declare const TbPercentage30: IconType;
export declare const TbPercentage33: IconType;
export declare const TbPercentage40: IconType;
export declare const TbPercentage50: IconType;
export declare const TbPercentage60: IconType;
export declare const TbPercentage66: IconType;
export declare const TbPercentage70: IconType;
export declare const TbPercentage75: IconType;
export declare const TbPercentage80: IconType;
export declare const TbPercentage90: IconType;
export declare const TbPercentage: IconType;
export declare const TbPerfume: IconType;
export declare const TbPerspectiveOff: IconType;
export declare const TbPerspective: IconType;
export declare const TbPhoneCall: IconType;
export declare const TbPhoneCalling: IconType;
export declare const TbPhoneCheck: IconType;
export declare const TbPhoneDone: IconType;
export declare const TbPhoneEnd: IconType;
export declare const TbPhoneIncoming: IconType;
export declare const TbPhoneOff: IconType;
export declare const TbPhoneOutgoing: IconType;
export declare const TbPhonePause: IconType;
export declare const TbPhonePlus: IconType;
export declare const TbPhoneRinging: IconType;
export declare const TbPhoneSpark: IconType;
export declare const TbPhoneX: IconType;
export declare const TbPhone: IconType;
export declare const TbPhotoAi: IconType;
export declare const TbPhotoBitcoin: IconType;
export declare const TbPhotoBolt: IconType;
export declare const TbPhotoCancel: IconType;
export declare const TbPhotoCheck: IconType;
export declare const TbPhotoCircleMinus: IconType;
export declare const TbPhotoCirclePlus: IconType;
export declare const TbPhotoCircle: IconType;
export declare const TbPhotoCode: IconType;
export declare const TbPhotoCog: IconType;
export declare const TbPhotoDollar: IconType;
export declare const TbPhotoDown: IconType;
export declare const TbPhotoEdit: IconType;
export declare const TbPhotoExclamation: IconType;
export declare const TbPhotoHeart: IconType;
export declare const TbPhotoHexagon: IconType;
export declare const TbPhotoMinus: IconType;
export declare const TbPhotoOff: IconType;
export declare const TbPhotoPause: IconType;
export declare const TbPhotoPentagon: IconType;
export declare const TbPhotoPin: IconType;
export declare const TbPhotoPlus: IconType;
export declare const TbPhotoQuestion: IconType;
export declare const TbPhotoScan: IconType;
export declare const TbPhotoSearch: IconType;
export declare const TbPhotoSensor2: IconType;
export declare const TbPhotoSensor3: IconType;
export declare const TbPhotoSensor: IconType;
export declare const TbPhotoShare: IconType;
export declare const TbPhotoShield: IconType;
export declare const TbPhotoSpark: IconType;
export declare const TbPhotoSquareRounded: IconType;
export declare const TbPhotoStar: IconType;
export declare const TbPhotoUp: IconType;
export declare const TbPhotoVideo: IconType;
export declare const TbPhotoX: IconType;
export declare const TbPhoto: IconType;
export declare const TbPhysotherapist: IconType;
export declare const TbPiano: IconType;
export declare const TbPick: IconType;
export declare const TbPicnicTable: IconType;
export declare const TbPictureInPictureOff: IconType;
export declare const TbPictureInPictureOn: IconType;
export declare const TbPictureInPictureTop: IconType;
export declare const TbPictureInPicture: IconType;
export declare const TbPigMoney: IconType;
export declare const TbPigOff: IconType;
export declare const TbPig: IconType;
export declare const TbPilcrowLeft: IconType;
export declare const TbPilcrowRight: IconType;
export declare const TbPilcrow: IconType;
export declare const TbPillOff: IconType;
export declare const TbPill: IconType;
export declare const TbPills: IconType;
export declare const TbPinEnd: IconType;
export declare const TbPinInvoke: IconType;
export declare const TbPin: IconType;
export declare const TbPingPong: IconType;
export declare const TbPinnedOff: IconType;
export declare const TbPinned: IconType;
export declare const TbPizzaOff: IconType;
export declare const TbPizza: IconType;
export declare const TbPlaceholder: IconType;
export declare const TbPlaneArrival: IconType;
export declare const TbPlaneDeparture: IconType;
export declare const TbPlaneInflight: IconType;
export declare const TbPlaneOff: IconType;
export declare const TbPlaneTilt: IconType;
export declare const TbPlane: IconType;
export declare const TbPlanetOff: IconType;
export declare const TbPlanet: IconType;
export declare const TbPlant2Off: IconType;
export declare const TbPlant2: IconType;
export declare const TbPlantOff: IconType;
export declare const TbPlant: IconType;
export declare const TbPlayBasketball: IconType;
export declare const TbPlayCard1: IconType;
export declare const TbPlayCard10: IconType;
export declare const TbPlayCard2: IconType;
export declare const TbPlayCard3: IconType;
export declare const TbPlayCard4: IconType;
export declare const TbPlayCard5: IconType;
export declare const TbPlayCard6: IconType;
export declare const TbPlayCard7: IconType;
export declare const TbPlayCard8: IconType;
export declare const TbPlayCard9: IconType;
export declare const TbPlayCardA: IconType;
export declare const TbPlayCardJ: IconType;
export declare const TbPlayCardK: IconType;
export declare const TbPlayCardOff: IconType;
export declare const TbPlayCardQ: IconType;
export declare const TbPlayCardStar: IconType;
export declare const TbPlayCard: IconType;
export declare const TbPlayFootball: IconType;
export declare const TbPlayHandball: IconType;
export declare const TbPlayVolleyball: IconType;
export declare const TbPlayerEject: IconType;
export declare const TbPlayerPause: IconType;
export declare const TbPlayerPlay: IconType;
export declare const TbPlayerRecord: IconType;
export declare const TbPlayerSkipBack: IconType;
export declare const TbPlayerSkipForward: IconType;
export declare const TbPlayerStop: IconType;
export declare const TbPlayerTrackNext: IconType;
export declare const TbPlayerTrackPrev: IconType;
export declare const TbPlaylistAdd: IconType;
export declare const TbPlaylistOff: IconType;
export declare const TbPlaylistX: IconType;
export declare const TbPlaylist: IconType;
export declare const TbPlaystationCircle: IconType;
export declare const TbPlaystationSquare: IconType;
export declare const TbPlaystationTriangle: IconType;
export declare const TbPlaystationX: IconType;
export declare const TbPlugConnectedX: IconType;
export declare const TbPlugConnected: IconType;
export declare const TbPlugOff: IconType;
export declare const TbPlugX: IconType;
export declare const TbPlug: IconType;
export declare const TbPlusEqual: IconType;
export declare const TbPlusMinus: IconType;
export declare const TbPlus: IconType;
export declare const TbPng: IconType;
export declare const TbPodiumOff: IconType;
export declare const TbPodium: IconType;
export declare const TbPointOff: IconType;
export declare const TbPoint: IconType;
export declare const TbPointerBolt: IconType;
export declare const TbPointerCancel: IconType;
export declare const TbPointerCheck: IconType;
export declare const TbPointerCode: IconType;
export declare const TbPointerCog: IconType;
export declare const TbPointerDollar: IconType;
export declare const TbPointerDown: IconType;
export declare const TbPointerExclamation: IconType;
export declare const TbPointerHeart: IconType;
export declare const TbPointerMinus: IconType;
export declare const TbPointerOff: IconType;
export declare const TbPointerPause: IconType;
export declare const TbPointerPin: IconType;
export declare const TbPointerPlus: IconType;
export declare const TbPointerQuestion: IconType;
export declare const TbPointerSearch: IconType;
export declare const TbPointerShare: IconType;
export declare const TbPointerStar: IconType;
export declare const TbPointerUp: IconType;
export declare const TbPointerX: IconType;
export declare const TbPointer: IconType;
export declare const TbPokeballOff: IconType;
export declare const TbPokeball: IconType;
export declare const TbPokerChip: IconType;
export declare const TbPolaroid: IconType;
export declare const TbPolygonOff: IconType;
export declare const TbPolygon: IconType;
export declare const TbPoo: IconType;
export declare const TbPoolOff: IconType;
export declare const TbPool: IconType;
export declare const TbPower: IconType;
export declare const TbPray: IconType;
export declare const TbPremiumRights: IconType;
export declare const TbPrescription: IconType;
export declare const TbPresentationAnalytics: IconType;
export declare const TbPresentationOff: IconType;
export declare const TbPresentation: IconType;
export declare const TbPrinterOff: IconType;
export declare const TbPrinter: IconType;
export declare const TbPrismLight: IconType;
export declare const TbPrismOff: IconType;
export declare const TbPrismPlus: IconType;
export declare const TbPrism: IconType;
export declare const TbPrison: IconType;
export declare const TbProgressAlert: IconType;
export declare const TbProgressBolt: IconType;
export declare const TbProgressCheck: IconType;
export declare const TbProgressDown: IconType;
export declare const TbProgressHelp: IconType;
export declare const TbProgressX: IconType;
export declare const TbProgress: IconType;
export declare const TbPrompt: IconType;
export declare const TbProng: IconType;
export declare const TbPropellerOff: IconType;
export declare const TbPropeller: IconType;
export declare const TbProtocol: IconType;
export declare const TbPumpkinScary: IconType;
export declare const TbPuzzle2: IconType;
export declare const TbPuzzleOff: IconType;
export declare const TbPuzzle: IconType;
export declare const TbPyramidOff: IconType;
export declare const TbPyramidPlus: IconType;
export declare const TbPyramid: IconType;
export declare const TbQrcodeOff: IconType;
export declare const TbQrcode: IconType;
export declare const TbQuestionMark: IconType;
export declare const TbQuoteOff: IconType;
export declare const TbQuote: IconType;
export declare const TbQuotes: IconType;
export declare const TbRadar2: IconType;
export declare const TbRadarOff: IconType;
export declare const TbRadar: IconType;
export declare const TbRadioOff: IconType;
export declare const TbRadio: IconType;
export declare const TbRadioactiveOff: IconType;
export declare const TbRadioactive: IconType;
export declare const TbRadiusBottomLeft: IconType;
export declare const TbRadiusBottomRight: IconType;
export declare const TbRadiusTopLeft: IconType;
export declare const TbRadiusTopRight: IconType;
export declare const TbRainbowOff: IconType;
export declare const TbRainbow: IconType;
export declare const TbRating12Plus: IconType;
export declare const TbRating14Plus: IconType;
export declare const TbRating16Plus: IconType;
export declare const TbRating18Plus: IconType;
export declare const TbRating21Plus: IconType;
export declare const TbRazorElectric: IconType;
export declare const TbRazor: IconType;
export declare const TbReceipt2: IconType;
export declare const TbReceiptBitcoin: IconType;
export declare const TbReceiptDollar: IconType;
export declare const TbReceiptEuro: IconType;
export declare const TbReceiptOff: IconType;
export declare const TbReceiptPound: IconType;
export declare const TbReceiptRefund: IconType;
export declare const TbReceiptRupee: IconType;
export declare const TbReceiptTax: IconType;
export declare const TbReceiptYen: IconType;
export declare const TbReceiptYuan: IconType;
export declare const TbReceipt: IconType;
export declare const TbRecharging: IconType;
export declare const TbRecordMailOff: IconType;
export declare const TbRecordMail: IconType;
export declare const TbRectangleRoundedBottom: IconType;
export declare const TbRectangleRoundedTop: IconType;
export declare const TbRectangleVertical: IconType;
export declare const TbRectangle: IconType;
export declare const TbRectangularPrismOff: IconType;
export declare const TbRectangularPrismPlus: IconType;
export declare const TbRectangularPrism: IconType;
export declare const TbRecycleOff: IconType;
export declare const TbRecycle: IconType;
export declare const TbRefreshAlert: IconType;
export declare const TbRefreshDot: IconType;
export declare const TbRefreshOff: IconType;
export declare const TbRefresh: IconType;
export declare const TbRegexOff: IconType;
export declare const TbRegex: IconType;
export declare const TbRegistered: IconType;
export declare const TbRelationManyToMany: IconType;
export declare const TbRelationOneToMany: IconType;
export declare const TbRelationOneToOne: IconType;
export declare const TbReload: IconType;
export declare const TbReorder: IconType;
export declare const TbRepeatOff: IconType;
export declare const TbRepeatOnce: IconType;
export declare const TbRepeat: IconType;
export declare const TbReplaceOff: IconType;
export declare const TbReplace: IconType;
export declare const TbReportAnalytics: IconType;
export declare const TbReportMedical: IconType;
export declare const TbReportMoney: IconType;
export declare const TbReportOff: IconType;
export declare const TbReportSearch: IconType;
export declare const TbReport: IconType;
export declare const TbReservedLine: IconType;
export declare const TbResize: IconType;
export declare const TbRestore: IconType;
export declare const TbRewindBackward10: IconType;
export declare const TbRewindBackward15: IconType;
export declare const TbRewindBackward20: IconType;
export declare const TbRewindBackward30: IconType;
export declare const TbRewindBackward40: IconType;
export declare const TbRewindBackward5: IconType;
export declare const TbRewindBackward50: IconType;
export declare const TbRewindBackward60: IconType;
export declare const TbRewindForward10: IconType;
export declare const TbRewindForward15: IconType;
export declare const TbRewindForward20: IconType;
export declare const TbRewindForward30: IconType;
export declare const TbRewindForward40: IconType;
export declare const TbRewindForward5: IconType;
export declare const TbRewindForward50: IconType;
export declare const TbRewindForward60: IconType;
export declare const TbRibbonHealth: IconType;
export declare const TbRings: IconType;
export declare const TbRippleOff: IconType;
export declare const TbRipple: IconType;
export declare const TbRoadOff: IconType;
export declare const TbRoadSign: IconType;
export declare const TbRoad: IconType;
export declare const TbRobotFace: IconType;
export declare const TbRobotOff: IconType;
export declare const TbRobot: IconType;
export declare const TbRocketOff: IconType;
export declare const TbRocket: IconType;
export declare const TbRollerSkating: IconType;
export declare const TbRollercoasterOff: IconType;
export declare const TbRollercoaster: IconType;
export declare const TbRosetteDiscountCheckOff: IconType;
export declare const TbRosetteDiscountCheck: IconType;
export declare const TbRosetteDiscountOff: IconType;
export declare const TbRosetteDiscount: IconType;
export declare const TbRosetteNumber0: IconType;
export declare const TbRosetteNumber1: IconType;
export declare const TbRosetteNumber2: IconType;
export declare const TbRosetteNumber3: IconType;
export declare const TbRosetteNumber4: IconType;
export declare const TbRosetteNumber5: IconType;
export declare const TbRosetteNumber6: IconType;
export declare const TbRosetteNumber7: IconType;
export declare const TbRosetteNumber8: IconType;
export declare const TbRosetteNumber9: IconType;
export declare const TbRosette: IconType;
export declare const TbRotate2: IconType;
export declare const TbRotate360: IconType;
export declare const TbRotate3D: IconType;
export declare const TbRotateClockwise2: IconType;
export declare const TbRotateClockwise: IconType;
export declare const TbRotateDot: IconType;
export declare const TbRotateRectangle: IconType;
export declare const TbRotate: IconType;
export declare const TbRoute2: IconType;
export declare const TbRouteAltLeft: IconType;
export declare const TbRouteAltRight: IconType;
export declare const TbRouteOff: IconType;
export declare const TbRouteScan: IconType;
export declare const TbRouteSquare2: IconType;
export declare const TbRouteSquare: IconType;
export declare const TbRouteX2: IconType;
export declare const TbRouteX: IconType;
export declare const TbRoute: IconType;
export declare const TbRouterOff: IconType;
export declare const TbRouter: IconType;
export declare const TbRowInsertBottom: IconType;
export declare const TbRowInsertTop: IconType;
export declare const TbRowRemove: IconType;
export declare const TbRss: IconType;
export declare const TbRubberStampOff: IconType;
export declare const TbRubberStamp: IconType;
export declare const TbRuler2Off: IconType;
export declare const TbRuler2: IconType;
export declare const TbRuler3: IconType;
export declare const TbRulerMeasure2: IconType;
export declare const TbRulerMeasure: IconType;
export declare const TbRulerOff: IconType;
export declare const TbRuler: IconType;
export declare const TbRun: IconType;
export declare const TbRvTruck: IconType;
export declare const TbSTurnDown: IconType;
export declare const TbSTurnLeft: IconType;
export declare const TbSTurnRight: IconType;
export declare const TbSTurnUp: IconType;
export declare const TbSailboat2: IconType;
export declare const TbSailboatOff: IconType;
export declare const TbSailboat: IconType;
export declare const TbSalad: IconType;
export declare const TbSalt: IconType;
export declare const TbSandbox: IconType;
export declare const TbSatelliteOff: IconType;
export declare const TbSatellite: IconType;
export declare const TbSausage: IconType;
export declare const TbScaleOff: IconType;
export declare const TbScaleOutlineOff: IconType;
export declare const TbScaleOutline: IconType;
export declare const TbScale: IconType;
export declare const TbScanEye: IconType;
export declare const TbScanPosition: IconType;
export declare const TbScan: IconType;
export declare const TbSchemaOff: IconType;
export declare const TbSchema: IconType;
export declare const TbSchoolBell: IconType;
export declare const TbSchoolOff: IconType;
export declare const TbSchool: IconType;
export declare const TbScissorsOff: IconType;
export declare const TbScissors: IconType;
export declare const TbScooterElectric: IconType;
export declare const TbScooter: IconType;
export declare const TbScoreboard: IconType;
export declare const TbScreenShareOff: IconType;
export declare const TbScreenShare: IconType;
export declare const TbScreenshot: IconType;
export declare const TbScribbleOff: IconType;
export declare const TbScribble: IconType;
export declare const TbScriptMinus: IconType;
export declare const TbScriptPlus: IconType;
export declare const TbScriptX: IconType;
export declare const TbScript: IconType;
export declare const TbScubaDivingTank: IconType;
export declare const TbScubaDiving: IconType;
export declare const TbScubaMaskOff: IconType;
export declare const TbScubaMask: IconType;
export declare const TbSdk: IconType;
export declare const TbSearchOff: IconType;
export declare const TbSearch: IconType;
export declare const TbSectionSign: IconType;
export declare const TbSection: IconType;
export declare const TbSeedingOff: IconType;
export declare const TbSeeding: IconType;
export declare const TbSelectAll: IconType;
export declare const TbSelect: IconType;
export declare const TbSelector: IconType;
export declare const TbSend2: IconType;
export declare const TbSendOff: IconType;
export declare const TbSend: IconType;
export declare const TbSeo: IconType;
export declare const TbSeparatorHorizontal: IconType;
export declare const TbSeparatorVertical: IconType;
export declare const TbSeparator: IconType;
export declare const TbServer2: IconType;
export declare const TbServerBolt: IconType;
export declare const TbServerCog: IconType;
export declare const TbServerOff: IconType;
export declare const TbServerSpark: IconType;
export declare const TbServer: IconType;
export declare const TbServicemark: IconType;
export declare const TbSettings2: IconType;
export declare const TbSettingsAutomation: IconType;
export declare const TbSettingsBolt: IconType;
export declare const TbSettingsCancel: IconType;
export declare const TbSettingsCheck: IconType;
export declare const TbSettingsCode: IconType;
export declare const TbSettingsCog: IconType;
export declare const TbSettingsDollar: IconType;
export declare const TbSettingsDown: IconType;
export declare const TbSettingsExclamation: IconType;
export declare const TbSettingsHeart: IconType;
export declare const TbSettingsMinus: IconType;
export declare const TbSettingsOff: IconType;
export declare const TbSettingsPause: IconType;
export declare const TbSettingsPin: IconType;
export declare const TbSettingsPlus: IconType;
export declare const TbSettingsQuestion: IconType;
export declare const TbSettingsSearch: IconType;
export declare const TbSettingsShare: IconType;
export declare const TbSettingsSpark: IconType;
export declare const TbSettingsStar: IconType;
export declare const TbSettingsUp: IconType;
export declare const TbSettingsX: IconType;
export declare const TbSettings: IconType;
export declare const TbShadowOff: IconType;
export declare const TbShadow: IconType;
export declare const TbShape2: IconType;
export declare const TbShape3: IconType;
export declare const TbShapeOff: IconType;
export declare const TbShape: IconType;
export declare const TbShare2: IconType;
export declare const TbShare3: IconType;
export declare const TbShareOff: IconType;
export declare const TbShare: IconType;
export declare const TbShareplay: IconType;
export declare const TbShieldBolt: IconType;
export declare const TbShieldCancel: IconType;
export declare const TbShieldCheck: IconType;
export declare const TbShieldCheckered: IconType;
export declare const TbShieldChevron: IconType;
export declare const TbShieldCode: IconType;
export declare const TbShieldCog: IconType;
export declare const TbShieldDollar: IconType;
export declare const TbShieldDown: IconType;
export declare const TbShieldExclamation: IconType;
export declare const TbShieldHalf: IconType;
export declare const TbShieldHeart: IconType;
export declare const TbShieldLock: IconType;
export declare const TbShieldMinus: IconType;
export declare const TbShieldOff: IconType;
export declare const TbShieldPause: IconType;
export declare const TbShieldPin: IconType;
export declare const TbShieldPlus: IconType;
export declare const TbShieldQuestion: IconType;
export declare const TbShieldSearch: IconType;
export declare const TbShieldShare: IconType;
export declare const TbShieldStar: IconType;
export declare const TbShieldUp: IconType;
export declare const TbShieldX: IconType;
export declare const TbShield: IconType;
export declare const TbShipOff: IconType;
export declare const TbShip: IconType;
export declare const TbShirtOff: IconType;
export declare const TbShirtSport: IconType;
export declare const TbShirt: IconType;
export declare const TbShoeOff: IconType;
export declare const TbShoe: IconType;
export declare const TbShoppingBagCheck: IconType;
export declare const TbShoppingBagDiscount: IconType;
export declare const TbShoppingBagEdit: IconType;
export declare const TbShoppingBagExclamation: IconType;
export declare const TbShoppingBagHeart: IconType;
export declare const TbShoppingBagMinus: IconType;
export declare const TbShoppingBagPlus: IconType;
export declare const TbShoppingBagSearch: IconType;
export declare const TbShoppingBagX: IconType;
export declare const TbShoppingBag: IconType;
export declare const TbShoppingCartBolt: IconType;
export declare const TbShoppingCartCancel: IconType;
export declare const TbShoppingCartCheck: IconType;
export declare const TbShoppingCartCode: IconType;
export declare const TbShoppingCartCog: IconType;
export declare const TbShoppingCartCopy: IconType;
export declare const TbShoppingCartDiscount: IconType;
export declare const TbShoppingCartDollar: IconType;
export declare const TbShoppingCartDown: IconType;
export declare const TbShoppingCartExclamation: IconType;
export declare const TbShoppingCartHeart: IconType;
export declare const TbShoppingCartMinus: IconType;
export declare const TbShoppingCartOff: IconType;
export declare const TbShoppingCartPause: IconType;
export declare const TbShoppingCartPin: IconType;
export declare const TbShoppingCartPlus: IconType;
export declare const TbShoppingCartQuestion: IconType;
export declare const TbShoppingCartSearch: IconType;
export declare const TbShoppingCartShare: IconType;
export declare const TbShoppingCartStar: IconType;
export declare const TbShoppingCartUp: IconType;
export declare const TbShoppingCartX: IconType;
export declare const TbShoppingCart: IconType;
export declare const TbShovelPitchforks: IconType;
export declare const TbShovel: IconType;
export declare const TbShredder: IconType;
export declare const TbSignLeft: IconType;
export declare const TbSignRight: IconType;
export declare const TbSignal2G: IconType;
export declare const TbSignal3G: IconType;
export declare const TbSignal4gPlus: IconType;
export declare const TbSignal4G: IconType;
export declare const TbSignal5G: IconType;
export declare const TbSignal6G: IconType;
export declare const TbSignalE: IconType;
export declare const TbSignalG: IconType;
export declare const TbSignalHPlus: IconType;
export declare const TbSignalH: IconType;
export declare const TbSignalLte: IconType;
export declare const TbSignatureOff: IconType;
export declare const TbSignature: IconType;
export declare const TbSitemapOff: IconType;
export declare const TbSitemap: IconType;
export declare const TbSkateboardOff: IconType;
export declare const TbSkateboard: IconType;
export declare const TbSkateboarding: IconType;
export declare const TbSkewX: IconType;
export declare const TbSkewY: IconType;
export declare const TbSkiJumping: IconType;
export declare const TbSkull: IconType;
export declare const TbSlash: IconType;
export declare const TbSlashes: IconType;
export declare const TbSleigh: IconType;
export declare const TbSlice: IconType;
export declare const TbSlideshow: IconType;
export declare const TbSmartHomeOff: IconType;
export declare const TbSmartHome: IconType;
export declare const TbSmokingNo: IconType;
export declare const TbSmoking: IconType;
export declare const TbSnowboarding: IconType;
export declare const TbSnowflakeOff: IconType;
export declare const TbSnowflake: IconType;
export declare const TbSnowman: IconType;
export declare const TbSoccerField: IconType;
export declare const TbSocialOff: IconType;
export declare const TbSocial: IconType;
export declare const TbSock: IconType;
export declare const TbSofaOff: IconType;
export declare const TbSofa: IconType;
export declare const TbSolarElectricity: IconType;
export declare const TbSolarPanel2: IconType;
export declare const TbSolarPanel: IconType;
export declare const TbSort09: IconType;
export declare const TbSort90: IconType;
export declare const TbSortAZ: IconType;
export declare const TbSortAscending2: IconType;
export declare const TbSortAscendingLetters: IconType;
export declare const TbSortAscendingNumbers: IconType;
export declare const TbSortAscendingShapes: IconType;
export declare const TbSortAscendingSmallBig: IconType;
export declare const TbSortAscending: IconType;
export declare const TbSortDescending2: IconType;
export declare const TbSortDescendingLetters: IconType;
export declare const TbSortDescendingNumbers: IconType;
export declare const TbSortDescendingShapes: IconType;
export declare const TbSortDescendingSmallBig: IconType;
export declare const TbSortDescending: IconType;
export declare const TbSortZA: IconType;
export declare const TbSos: IconType;
export declare const TbSoupOff: IconType;
export declare const TbSoup: IconType;
export declare const TbSourceCode: IconType;
export declare const TbSpaceOff: IconType;
export declare const TbSpace: IconType;
export declare const TbSpaces: IconType;
export declare const TbSpacingHorizontal: IconType;
export declare const TbSpacingVertical: IconType;
export declare const TbSpade: IconType;
export declare const TbSparkles: IconType;
export declare const TbSpeakerphone: IconType;
export declare const TbSpeedboat: IconType;
export declare const TbSphereOff: IconType;
export declare const TbSpherePlus: IconType;
export declare const TbSphere: IconType;
export declare const TbSpider: IconType;
export declare const TbSpiralOff: IconType;
export declare const TbSpiral: IconType;
export declare const TbSportBillard: IconType;
export declare const TbSpray: IconType;
export declare const TbSpyOff: IconType;
export declare const TbSpy: IconType;
export declare const TbSql: IconType;
export declare const TbSquareArrowDown: IconType;
export declare const TbSquareArrowLeft: IconType;
export declare const TbSquareArrowRight: IconType;
export declare const TbSquareArrowUp: IconType;
export declare const TbSquareAsterisk: IconType;
export declare const TbSquareCheck: IconType;
export declare const TbSquareChevronDown: IconType;
export declare const TbSquareChevronLeft: IconType;
export declare const TbSquareChevronRight: IconType;
export declare const TbSquareChevronUp: IconType;
export declare const TbSquareChevronsDown: IconType;
export declare const TbSquareChevronsLeft: IconType;
export declare const TbSquareChevronsRight: IconType;
export declare const TbSquareChevronsUp: IconType;
export declare const TbSquareDashed: IconType;
export declare const TbSquareDot: IconType;
export declare const TbSquareF0: IconType;
export declare const TbSquareF1: IconType;
export declare const TbSquareF2: IconType;
export declare const TbSquareF3: IconType;
export declare const TbSquareF4: IconType;
export declare const TbSquareF5: IconType;
export declare const TbSquareF6: IconType;
export declare const TbSquareF7: IconType;
export declare const TbSquareF8: IconType;
export declare const TbSquareF9: IconType;
export declare const TbSquareForbid2: IconType;
export declare const TbSquareForbid: IconType;
export declare const TbSquareHalf: IconType;
export declare const TbSquareKey: IconType;
export declare const TbSquareLetterA: IconType;
export declare const TbSquareLetterB: IconType;
export declare const TbSquareLetterC: IconType;
export declare const TbSquareLetterD: IconType;
export declare const TbSquareLetterE: IconType;
export declare const TbSquareLetterF: IconType;
export declare const TbSquareLetterG: IconType;
export declare const TbSquareLetterH: IconType;
export declare const TbSquareLetterI: IconType;
export declare const TbSquareLetterJ: IconType;
export declare const TbSquareLetterK: IconType;
export declare const TbSquareLetterL: IconType;
export declare const TbSquareLetterM: IconType;
export declare const TbSquareLetterN: IconType;
export declare const TbSquareLetterO: IconType;
export declare const TbSquareLetterP: IconType;
export declare const TbSquareLetterQ: IconType;
export declare const TbSquareLetterR: IconType;
export declare const TbSquareLetterS: IconType;
export declare const TbSquareLetterT: IconType;
export declare const TbSquareLetterU: IconType;
export declare const TbSquareLetterV: IconType;
export declare const TbSquareLetterW: IconType;
export declare const TbSquareLetterX: IconType;
export declare const TbSquareLetterY: IconType;
export declare const TbSquareLetterZ: IconType;
export declare const TbSquareMinus: IconType;
export declare const TbSquareNumber0: IconType;
export declare const TbSquareNumber1: IconType;
export declare const TbSquareNumber2: IconType;
export declare const TbSquareNumber3: IconType;
export declare const TbSquareNumber4: IconType;
export declare const TbSquareNumber5: IconType;
export declare const TbSquareNumber6: IconType;
export declare const TbSquareNumber7: IconType;
export declare const TbSquareNumber8: IconType;
export declare const TbSquareNumber9: IconType;
export declare const TbSquareOff: IconType;
export declare const TbSquarePercentage: IconType;
export declare const TbSquarePlus2: IconType;
export declare const TbSquarePlus: IconType;
export declare const TbSquareRoot2: IconType;
export declare const TbSquareRoot: IconType;
export declare const TbSquareRotatedForbid2: IconType;
export declare const TbSquareRotatedForbid: IconType;
export declare const TbSquareRotatedOff: IconType;
export declare const TbSquareRotated: IconType;
export declare const TbSquareRoundedArrowDown: IconType;
export declare const TbSquareRoundedArrowLeft: IconType;
export declare const TbSquareRoundedArrowRight: IconType;
export declare const TbSquareRoundedArrowUp: IconType;
export declare const TbSquareRoundedCheck: IconType;
export declare const TbSquareRoundedChevronDown: IconType;
export declare const TbSquareRoundedChevronLeft: IconType;
export declare const TbSquareRoundedChevronRight: IconType;
export declare const TbSquareRoundedChevronUp: IconType;
export declare const TbSquareRoundedChevronsDown: IconType;
export declare const TbSquareRoundedChevronsLeft: IconType;
export declare const TbSquareRoundedChevronsRight: IconType;
export declare const TbSquareRoundedChevronsUp: IconType;
export declare const TbSquareRoundedLetterA: IconType;
export declare const TbSquareRoundedLetterB: IconType;
export declare const TbSquareRoundedLetterC: IconType;
export declare const TbSquareRoundedLetterD: IconType;
export declare const TbSquareRoundedLetterE: IconType;
export declare const TbSquareRoundedLetterF: IconType;
export declare const TbSquareRoundedLetterG: IconType;
export declare const TbSquareRoundedLetterH: IconType;
export declare const TbSquareRoundedLetterI: IconType;
export declare const TbSquareRoundedLetterJ: IconType;
export declare const TbSquareRoundedLetterK: IconType;
export declare const TbSquareRoundedLetterL: IconType;
export declare const TbSquareRoundedLetterM: IconType;
export declare const TbSquareRoundedLetterN: IconType;
export declare const TbSquareRoundedLetterO: IconType;
export declare const TbSquareRoundedLetterP: IconType;
export declare const TbSquareRoundedLetterQ: IconType;
export declare const TbSquareRoundedLetterR: IconType;
export declare const TbSquareRoundedLetterS: IconType;
export declare const TbSquareRoundedLetterT: IconType;
export declare const TbSquareRoundedLetterU: IconType;
export declare const TbSquareRoundedLetterV: IconType;
export declare const TbSquareRoundedLetterW: IconType;
export declare const TbSquareRoundedLetterX: IconType;
export declare const TbSquareRoundedLetterY: IconType;
export declare const TbSquareRoundedLetterZ: IconType;
export declare const TbSquareRoundedMinus2: IconType;
export declare const TbSquareRoundedMinus: IconType;
export declare const TbSquareRoundedNumber0: IconType;
export declare const TbSquareRoundedNumber1: IconType;
export declare const TbSquareRoundedNumber2: IconType;
export declare const TbSquareRoundedNumber3: IconType;
export declare const TbSquareRoundedNumber4: IconType;
export declare const TbSquareRoundedNumber5: IconType;
export declare const TbSquareRoundedNumber6: IconType;
export declare const TbSquareRoundedNumber7: IconType;
export declare const TbSquareRoundedNumber8: IconType;
export declare const TbSquareRoundedNumber9: IconType;
export declare const TbSquareRoundedPercentage: IconType;
export declare const TbSquareRoundedPlus2: IconType;
export declare const TbSquareRoundedPlus: IconType;
export declare const TbSquareRoundedX: IconType;
export declare const TbSquareRounded: IconType;
export declare const TbSquareToggleHorizontal: IconType;
export declare const TbSquareToggle: IconType;
export declare const TbSquareX: IconType;
export declare const TbSquare: IconType;
export declare const TbSquaresDiagonal: IconType;
export declare const TbSquaresSelected: IconType;
export declare const TbSquares: IconType;
export declare const TbStack2: IconType;
export declare const TbStack3: IconType;
export declare const TbStackBack: IconType;
export declare const TbStackBackward: IconType;
export declare const TbStackForward: IconType;
export declare const TbStackFront: IconType;
export declare const TbStackMiddle: IconType;
export declare const TbStackPop: IconType;
export declare const TbStackPush: IconType;
export declare const TbStack: IconType;
export declare const TbStairsDown: IconType;
export declare const TbStairsUp: IconType;
export declare const TbStairs: IconType;
export declare const TbStarHalf: IconType;
export declare const TbStarOff: IconType;
export declare const TbStar: IconType;
export declare const TbStarsOff: IconType;
export declare const TbStars: IconType;
export declare const TbStatusChange: IconType;
export declare const TbSteam: IconType;
export declare const TbSteeringWheelOff: IconType;
export declare const TbSteeringWheel: IconType;
export declare const TbStepInto: IconType;
export declare const TbStepOut: IconType;
export declare const TbStereoGlasses: IconType;
export declare const TbStethoscopeOff: IconType;
export declare const TbStethoscope: IconType;
export declare const TbSticker2: IconType;
export declare const TbSticker: IconType;
export declare const TbStopwatch: IconType;
export declare const TbStormOff: IconType;
export declare const TbStorm: IconType;
export declare const TbStretching2: IconType;
export declare const TbStretching: IconType;
export declare const TbStrikethrough: IconType;
export declare const TbSubmarine: IconType;
export declare const TbSubscript: IconType;
export declare const TbSubtask: IconType;
export declare const TbSumOff: IconType;
export declare const TbSum: IconType;
export declare const TbSunElectricity: IconType;
export declare const TbSunHigh: IconType;
export declare const TbSunLow: IconType;
export declare const TbSunMoon: IconType;
export declare const TbSunOff: IconType;
export declare const TbSunWind: IconType;
export declare const TbSun: IconType;
export declare const TbSunglasses: IconType;
export declare const TbSunrise: IconType;
export declare const TbSunset2: IconType;
export declare const TbSunset: IconType;
export declare const TbSuperscript: IconType;
export declare const TbSvg: IconType;
export declare const TbSwimming: IconType;
export declare const TbSwipeDown: IconType;
export declare const TbSwipeLeft: IconType;
export declare const TbSwipeRight: IconType;
export declare const TbSwipeUp: IconType;
export declare const TbSwipe: IconType;
export declare const TbSwitch2: IconType;
export declare const TbSwitch3: IconType;
export declare const TbSwitchHorizontal: IconType;
export declare const TbSwitchVertical: IconType;
export declare const TbSwitch: IconType;
export declare const TbSwordOff: IconType;
export declare const TbSword: IconType;
export declare const TbSwords: IconType;
export declare const TbTableAlias: IconType;
export declare const TbTableColumn: IconType;
export declare const TbTableDashed: IconType;
export declare const TbTableDown: IconType;
export declare const TbTableExport: IconType;
export declare const TbTableHeart: IconType;
export declare const TbTableImport: IconType;
export declare const TbTableMinus: IconType;
export declare const TbTableOff: IconType;
export declare const TbTableOptions: IconType;
export declare const TbTablePlus: IconType;
export declare const TbTableRow: IconType;
export declare const TbTableShare: IconType;
export declare const TbTableShortcut: IconType;
export declare const TbTableSpark: IconType;
export declare const TbTable: IconType;
export declare const TbTagMinus: IconType;
export declare const TbTagOff: IconType;
export declare const TbTagPlus: IconType;
export declare const TbTagStarred: IconType;
export declare const TbTag: IconType;
export declare const TbTagsOff: IconType;
export declare const TbTags: IconType;
export declare const TbTallymark1: IconType;
export declare const TbTallymark2: IconType;
export declare const TbTallymark3: IconType;
export declare const TbTallymark4: IconType;
export declare const TbTallymarks: IconType;
export declare const TbTank: IconType;
export declare const TbTargetArrow: IconType;
export declare const TbTargetOff: IconType;
export declare const TbTarget: IconType;
export declare const TbTaxEuro: IconType;
export declare const TbTaxPound: IconType;
export declare const TbTax: IconType;
export declare const TbTeapot: IconType;
export declare const TbTelescopeOff: IconType;
export declare const TbTelescope: IconType;
export declare const TbTemperatureCelsius: IconType;
export declare const TbTemperatureFahrenheit: IconType;
export declare const TbTemperatureMinus: IconType;
export declare const TbTemperatureOff: IconType;
export declare const TbTemperaturePlus: IconType;
export declare const TbTemperatureSnow: IconType;
export declare const TbTemperatureSun: IconType;
export declare const TbTemperature: IconType;
export declare const TbTemplateOff: IconType;
export declare const TbTemplate: IconType;
export declare const TbTentOff: IconType;
export declare const TbTent: IconType;
export declare const TbTerminal2: IconType;
export declare const TbTerminal: IconType;
export declare const TbTestPipe2: IconType;
export declare const TbTestPipeOff: IconType;
export declare const TbTestPipe: IconType;
export declare const TbTex: IconType;
export declare const TbTextCaption: IconType;
export declare const TbTextColor: IconType;
export declare const TbTextDecrease: IconType;
export declare const TbTextDirectionLtr: IconType;
export declare const TbTextDirectionRtl: IconType;
export declare const TbTextGrammar: IconType;
export declare const TbTextIncrease: IconType;
export declare const TbTextOrientation: IconType;
export declare const TbTextPlus: IconType;
export declare const TbTextRecognition: IconType;
export declare const TbTextResize: IconType;
export declare const TbTextScan2: IconType;
export declare const TbTextSize: IconType;
export declare const TbTextSpellcheck: IconType;
export declare const TbTextWrapColumn: IconType;
export declare const TbTextWrapDisabled: IconType;
export declare const TbTextWrap: IconType;
export declare const TbTexture: IconType;
export declare const TbTheater: IconType;
export declare const TbThermometer: IconType;
export declare const TbThumbDownOff: IconType;
export declare const TbThumbDown: IconType;
export declare const TbThumbUpOff: IconType;
export declare const TbThumbUp: IconType;
export declare const TbTicTac: IconType;
export declare const TbTicketOff: IconType;
export declare const TbTicket: IconType;
export declare const TbTie: IconType;
export declare const TbTilde: IconType;
export declare const TbTiltShiftOff: IconType;
export declare const TbTiltShift: IconType;
export declare const TbTimeDuration0: IconType;
export declare const TbTimeDuration10: IconType;
export declare const TbTimeDuration15: IconType;
export declare const TbTimeDuration30: IconType;
export declare const TbTimeDuration45: IconType;
export declare const TbTimeDuration5: IconType;
export declare const TbTimeDuration60: IconType;
export declare const TbTimeDuration90: IconType;
export declare const TbTimeDurationOff: IconType;
export declare const TbTimelineEventExclamation: IconType;
export declare const TbTimelineEventMinus: IconType;
export declare const TbTimelineEventPlus: IconType;
export declare const TbTimelineEventText: IconType;
export declare const TbTimelineEventX: IconType;
export declare const TbTimelineEvent: IconType;
export declare const TbTimeline: IconType;
export declare const TbTimezone: IconType;
export declare const TbTipJarEuro: IconType;
export declare const TbTipJarPound: IconType;
export declare const TbTipJar: IconType;
export declare const TbTir: IconType;
export declare const TbToggleLeft: IconType;
export declare const TbToggleRight: IconType;
export declare const TbToiletPaperOff: IconType;
export declare const TbToiletPaper: IconType;
export declare const TbToml: IconType;
export declare const TbTool: IconType;
export declare const TbToolsKitchen2Off: IconType;
export declare const TbToolsKitchen2: IconType;
export declare const TbToolsKitchen3: IconType;
export declare const TbToolsKitchenOff: IconType;
export declare const TbToolsKitchen: IconType;
export declare const TbToolsOff: IconType;
export declare const TbTools: IconType;
export declare const TbTooltip: IconType;
export declare const TbTopologyBus: IconType;
export declare const TbTopologyComplex: IconType;
export declare const TbTopologyFullHierarchy: IconType;
export declare const TbTopologyFull: IconType;
export declare const TbTopologyRing2: IconType;
export declare const TbTopologyRing3: IconType;
export declare const TbTopologyRing: IconType;
export declare const TbTopologyStar2: IconType;
export declare const TbTopologyStar3: IconType;
export declare const TbTopologyStarRing2: IconType;
export declare const TbTopologyStarRing3: IconType;
export declare const TbTopologyStarRing: IconType;
export declare const TbTopologyStar: IconType;
export declare const TbTorii: IconType;
export declare const TbTornado: IconType;
export declare const TbTournament: IconType;
export declare const TbTowerOff: IconType;
export declare const TbTower: IconType;
export declare const TbTrack: IconType;
export declare const TbTractor: IconType;
export declare const TbTrademark: IconType;
export declare const TbTrafficConeOff: IconType;
export declare const TbTrafficCone: IconType;
export declare const TbTrafficLightsOff: IconType;
export declare const TbTrafficLights: IconType;
export declare const TbTrain: IconType;
export declare const TbTransactionBitcoin: IconType;
export declare const TbTransactionDollar: IconType;
export declare const TbTransactionEuro: IconType;
export declare const TbTransactionPound: IconType;
export declare const TbTransactionRupee: IconType;
export declare const TbTransactionYen: IconType;
export declare const TbTransactionYuan: IconType;
export declare const TbTransferIn: IconType;
export declare const TbTransferOut: IconType;
export declare const TbTransferVertical: IconType;
export declare const TbTransfer: IconType;
export declare const TbTransformPointBottomLeft: IconType;
export declare const TbTransformPointBottomRight: IconType;
export declare const TbTransformPointTopLeft: IconType;
export declare const TbTransformPointTopRight: IconType;
export declare const TbTransformPoint: IconType;
export declare const TbTransform: IconType;
export declare const TbTransitionBottom: IconType;
export declare const TbTransitionLeft: IconType;
export declare const TbTransitionRight: IconType;
export declare const TbTransitionTop: IconType;
export declare const TbTrashOff: IconType;
export declare const TbTrashX: IconType;
export declare const TbTrash: IconType;
export declare const TbTreadmill: IconType;
export declare const TbTree: IconType;
export declare const TbTrees: IconType;
export declare const TbTrekking: IconType;
export declare const TbTrendingDown2: IconType;
export declare const TbTrendingDown3: IconType;
export declare const TbTrendingDown: IconType;
export declare const TbTrendingUp2: IconType;
export declare const TbTrendingUp3: IconType;
export declare const TbTrendingUp: IconType;
export declare const TbTriangleInverted: IconType;
export declare const TbTriangleMinus2: IconType;
export declare const TbTriangleMinus: IconType;
export declare const TbTriangleOff: IconType;
export declare const TbTrianglePlus2: IconType;
export declare const TbTrianglePlus: IconType;
export declare const TbTriangleSquareCircle: IconType;
export declare const TbTriangle: IconType;
export declare const TbTriangles: IconType;
export declare const TbTrident: IconType;
export declare const TbTrolley: IconType;
export declare const TbTrophyOff: IconType;
export declare const TbTrophy: IconType;
export declare const TbTrowel: IconType;
export declare const TbTruckDelivery: IconType;
export declare const TbTruckLoading: IconType;
export declare const TbTruckOff: IconType;
export declare const TbTruckReturn: IconType;
export declare const TbTruck: IconType;
export declare const TbTxt: IconType;
export declare const TbTypeface: IconType;
export declare const TbTypographyOff: IconType;
export declare const TbTypography: IconType;
export declare const TbUTurnLeft: IconType;
export declare const TbUTurnRight: IconType;
export declare const TbUfoOff: IconType;
export declare const TbUfo: IconType;
export declare const TbUhd: IconType;
export declare const TbUmbrella2: IconType;
export declare const TbUmbrellaClosed2: IconType;
export declare const TbUmbrellaClosed: IconType;
export declare const TbUmbrellaOff: IconType;
export declare const TbUmbrella: IconType;
export declare const TbUnderline: IconType;
export declare const TbUniverse: IconType;
export declare const TbUnlink: IconType;
export declare const TbUpload: IconType;
export declare const TbUrgent: IconType;
export declare const TbUsb: IconType;
export declare const TbUserBitcoin: IconType;
export declare const TbUserBolt: IconType;
export declare const TbUserCancel: IconType;
export declare const TbUserCheck: IconType;
export declare const TbUserCircle: IconType;
export declare const TbUserCode: IconType;
export declare const TbUserCog: IconType;
export declare const TbUserDollar: IconType;
export declare const TbUserDown: IconType;
export declare const TbUserEdit: IconType;
export declare const TbUserExclamation: IconType;
export declare const TbUserHeart: IconType;
export declare const TbUserHexagon: IconType;
export declare const TbUserMinus: IconType;
export declare const TbUserOff: IconType;
export declare const TbUserPause: IconType;
export declare const TbUserPentagon: IconType;
export declare const TbUserPin: IconType;
export declare const TbUserPlus: IconType;
export declare const TbUserQuestion: IconType;
export declare const TbUserScan: IconType;
export declare const TbUserScreen: IconType;
export declare const TbUserSearch: IconType;
export declare const TbUserShare: IconType;
export declare const TbUserShield: IconType;
export declare const TbUserSquareRounded: IconType;
export declare const TbUserSquare: IconType;
export declare const TbUserStar: IconType;
export declare const TbUserUp: IconType;
export declare const TbUserX: IconType;
export declare const TbUser: IconType;
export declare const TbUsersGroup: IconType;
export declare const TbUsersMinus: IconType;
export declare const TbUsersPlus: IconType;
export declare const TbUsers: IconType;
export declare const TbUvIndex: IconType;
export declare const TbUxCircle: IconType;
export declare const TbVaccineBottleOff: IconType;
export declare const TbVaccineBottle: IconType;
export declare const TbVaccineOff: IconType;
export declare const TbVaccine: IconType;
export declare const TbVacuumCleaner: IconType;
export declare const TbVariableMinus: IconType;
export declare const TbVariableOff: IconType;
export declare const TbVariablePlus: IconType;
export declare const TbVariable: IconType;
export declare const TbVectorBezier2: IconType;
export declare const TbVectorBezierArc: IconType;
export declare const TbVectorBezierCircle: IconType;
export declare const TbVectorBezier: IconType;
export declare const TbVectorOff: IconType;
export declare const TbVectorSpline: IconType;
export declare const TbVectorTriangleOff: IconType;
export declare const TbVectorTriangle: IconType;
export declare const TbVector: IconType;
export declare const TbVenus: IconType;
export declare const TbVersionsOff: IconType;
export declare const TbVersions: IconType;
export declare const TbVideoMinus: IconType;
export declare const TbVideoOff: IconType;
export declare const TbVideoPlus: IconType;
export declare const TbVideo: IconType;
export declare const TbView360Arrow: IconType;
export declare const TbView360Number: IconType;
export declare const TbView360Off: IconType;
export declare const TbView360: IconType;
export declare const TbViewfinderOff: IconType;
export declare const TbViewfinder: IconType;
export declare const TbViewportNarrow: IconType;
export declare const TbViewportShort: IconType;
export declare const TbViewportTall: IconType;
export declare const TbViewportWide: IconType;
export declare const TbVinyl: IconType;
export declare const TbVipOff: IconType;
export declare const TbVip: IconType;
export declare const TbVirusOff: IconType;
export declare const TbVirusSearch: IconType;
export declare const TbVirus: IconType;
export declare const TbVocabularyOff: IconType;
export declare const TbVocabulary: IconType;
export declare const TbVolcano: IconType;
export declare const TbVolume2: IconType;
export declare const TbVolume3: IconType;
export declare const TbVolumeOff: IconType;
export declare const TbVolume: IconType;
export declare const TbVs: IconType;
export declare const TbWalk: IconType;
export declare const TbWallOff: IconType;
export declare const TbWall: IconType;
export declare const TbWalletOff: IconType;
export declare const TbWallet: IconType;
export declare const TbWallpaperOff: IconType;
export declare const TbWallpaper: IconType;
export declare const TbWandOff: IconType;
export declare const TbWand: IconType;
export declare const TbWashDry1: IconType;
export declare const TbWashDry2: IconType;
export declare const TbWashDry3: IconType;
export declare const TbWashDryA: IconType;
export declare const TbWashDryDip: IconType;
export declare const TbWashDryF: IconType;
export declare const TbWashDryFlat: IconType;
export declare const TbWashDryHang: IconType;
export declare const TbWashDryOff: IconType;
export declare const TbWashDryP: IconType;
export declare const TbWashDryShade: IconType;
export declare const TbWashDryW: IconType;
export declare const TbWashDry: IconType;
export declare const TbWashDrycleanOff: IconType;
export declare const TbWashDryclean: IconType;
export declare const TbWashEco: IconType;
export declare const TbWashGentle: IconType;
export declare const TbWashHand: IconType;
export declare const TbWashMachine: IconType;
export declare const TbWashOff: IconType;
export declare const TbWashPress: IconType;
export declare const TbWashTemperature1: IconType;
export declare const TbWashTemperature2: IconType;
export declare const TbWashTemperature3: IconType;
export declare const TbWashTemperature4: IconType;
export declare const TbWashTemperature5: IconType;
export declare const TbWashTemperature6: IconType;
export declare const TbWashTumbleDry: IconType;
export declare const TbWashTumbleOff: IconType;
export declare const TbWash: IconType;
export declare const TbWaterpolo: IconType;
export declare const TbWaveSawTool: IconType;
export declare const TbWaveSine: IconType;
export declare const TbWaveSquare: IconType;
export declare const TbWavesElectricity: IconType;
export declare const TbWebhookOff: IconType;
export declare const TbWebhook: IconType;
export declare const TbWeight: IconType;
export declare const TbWheatOff: IconType;
export declare const TbWheat: IconType;
export declare const TbWheel: IconType;
export declare const TbWheelchairOff: IconType;
export declare const TbWheelchair: IconType;
export declare const TbWhirl: IconType;
export declare const TbWifi0: IconType;
export declare const TbWifi1: IconType;
export declare const TbWifi2: IconType;
export declare const TbWifiOff: IconType;
export declare const TbWifi: IconType;
export declare const TbWindElectricity: IconType;
export declare const TbWindOff: IconType;
export declare const TbWind: IconType;
export declare const TbWindmillOff: IconType;
export declare const TbWindmill: IconType;
export declare const TbWindowMaximize: IconType;
export declare const TbWindowMinimize: IconType;
export declare const TbWindowOff: IconType;
export declare const TbWindow: IconType;
export declare const TbWindsock: IconType;
export declare const TbWiperWash: IconType;
export declare const TbWiper: IconType;
export declare const TbWoman: IconType;
export declare const TbWood: IconType;
export declare const TbWorldBolt: IconType;
export declare const TbWorldCancel: IconType;
export declare const TbWorldCheck: IconType;
export declare const TbWorldCode: IconType;
export declare const TbWorldCog: IconType;
export declare const TbWorldDollar: IconType;
export declare const TbWorldDown: IconType;
export declare const TbWorldDownload: IconType;
export declare const TbWorldExclamation: IconType;
export declare const TbWorldHeart: IconType;
export declare const TbWorldLatitude: IconType;
export declare const TbWorldLongitude: IconType;
export declare const TbWorldMinus: IconType;
export declare const TbWorldOff: IconType;
export declare const TbWorldPause: IconType;
export declare const TbWorldPin: IconType;
export declare const TbWorldPlus: IconType;
export declare const TbWorldQuestion: IconType;
export declare const TbWorldSearch: IconType;
export declare const TbWorldShare: IconType;
export declare const TbWorldStar: IconType;
export declare const TbWorldUp: IconType;
export declare const TbWorldUpload: IconType;
export declare const TbWorldWww: IconType;
export declare const TbWorldX: IconType;
export declare const TbWorld: IconType;
export declare const TbWreckingBall: IconType;
export declare const TbWritingOff: IconType;
export declare const TbWritingSignOff: IconType;
export declare const TbWritingSign: IconType;
export declare const TbWriting: IconType;
export declare const TbXPowerY: IconType;
export declare const TbX: IconType;
export declare const TbXboxA: IconType;
export declare const TbXboxB: IconType;
export declare const TbXboxX: IconType;
export declare const TbXboxY: IconType;
export declare const TbXd: IconType;
export declare const TbXxx: IconType;
export declare const TbYinYang: IconType;
export declare const TbYoga: IconType;
export declare const TbZeppelinOff: IconType;
export declare const TbZeppelin: IconType;
export declare const TbZip: IconType;
export declare const TbZodiacAquarius: IconType;
export declare const TbZodiacAries: IconType;
export declare const TbZodiacCancer: IconType;
export declare const TbZodiacCapricorn: IconType;
export declare const TbZodiacGemini: IconType;
export declare const TbZodiacLeo: IconType;
export declare const TbZodiacLibra: IconType;
export declare const TbZodiacPisces: IconType;
export declare const TbZodiacSagittarius: IconType;
export declare const TbZodiacScorpio: IconType;
export declare const TbZodiacTaurus: IconType;
export declare const TbZodiacVirgo: IconType;
export declare const TbZoomCancel: IconType;
export declare const TbZoomCheck: IconType;
export declare const TbZoomCode: IconType;
export declare const TbZoomExclamation: IconType;
export declare const TbZoomInArea: IconType;
export declare const TbZoomIn: IconType;
export declare const TbZoomMoney: IconType;
export declare const TbZoomOutArea: IconType;
export declare const TbZoomOut: IconType;
export declare const TbZoomPan: IconType;
export declare const TbZoomQuestion: IconType;
export declare const TbZoomReplace: IconType;
export declare const TbZoomReset: IconType;
export declare const TbZoomScan: IconType;
export declare const TbZoom: IconType;
export declare const TbZzzOff: IconType;
export declare const TbZzz: IconType;

'use client';

import { useState } from 'react';
import { Calendar, Eye, Clock, Share2, Bookmark, Tag } from 'lucide-react';
import { Article } from '@/types';

interface Props {
  article: Article;
}

export default function ArticleContent({ article }: Props) {
  const [isBookmarked, setIsBookmarked] = useState(false);

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const estimateReadingTime = (content: string) => {
    const wordsPerMinute = 200;
    const wordCount = content.split(' ').length;
    return Math.ceil(wordCount / wordsPerMinute);
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: article.title,
          text: article.excerpt,
          url: window.location.href,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      alert('Link copied to clipboard!');
    }
  };

  const renderContent = (content: string) => {
    // Simple markdown-like rendering
    return content
      .split('\n')
      .map((line, index) => {
        if (line.startsWith('# ')) {
          return <h1 key={index} className="text-3xl font-bold mt-8 mb-4">{line.substring(2)}</h1>;
        } else if (line.startsWith('## ')) {
          return <h2 key={index} className="text-2xl font-semibold mt-6 mb-3">{line.substring(3)}</h2>;
        } else if (line.startsWith('### ')) {
          return <h3 key={index} className="text-xl font-semibold mt-4 mb-2">{line.substring(4)}</h3>;
        } else if (line.startsWith('- ')) {
          return <li key={index} className="ml-4">{line.substring(2)}</li>;
        } else if (line.trim() === '') {
          return <br key={index} />;
        } else {
          return <p key={index} className="mb-4 leading-relaxed">{line}</p>;
        }
      });
  };

  return (
    <article className="bg-white rounded-lg shadow-lg overflow-hidden">
      {/* Featured Image */}
      {article.featuredImage && (
        <div className="relative h-64 md:h-96">
          <img
            src={article.featuredImage}
            alt={article.title}
            className="w-full h-full object-cover"
          />
          <div className="absolute top-4 left-4">
            <span className="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">
              {article.category}
            </span>
          </div>
        </div>
      )}

      <div className="p-6 md:p-8">
        {/* Article Header */}
        <header className="mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 leading-tight">
            {article.title}
          </h1>
          
          <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-6">
            <div className="flex items-center">
              <Calendar className="h-4 w-4 mr-1" />
              {formatDate(article.publishedAt)}
            </div>
            <div className="flex items-center">
              <Eye className="h-4 w-4 mr-1" />
              {article.views} views
            </div>
            <div className="flex items-center">
              <Clock className="h-4 w-4 mr-1" />
              {estimateReadingTime(article.content)} min read
            </div>
            <div className="flex items-center">
              <span className="text-gray-500">By</span>
              <span className="ml-1 font-medium">{article.author}</span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-4 pb-6 border-b">
            <button
              onClick={handleShare}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Share2 className="h-4 w-4" />
              Share
            </button>
            <button
              onClick={() => setIsBookmarked(!isBookmarked)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors ${
                isBookmarked
                  ? 'bg-yellow-50 border-yellow-300 text-yellow-700'
                  : 'border-gray-300 text-gray-700 hover:bg-gray-50'
              }`}
            >
              <Bookmark className={`h-4 w-4 ${isBookmarked ? 'fill-current' : ''}`} />
              {isBookmarked ? 'Bookmarked' : 'Bookmark'}
            </button>
          </div>
        </header>

        {/* Article Content */}
        <div className="prose prose-lg max-w-none">
          <div className="text-gray-800 leading-relaxed">
            {renderContent(article.content)}
          </div>
        </div>

        {/* Media Content */}
        {article.media && article.media.length > 0 && (
          <div className="mt-8">
            <h3 className="text-xl font-semibold mb-4">Related Media</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {article.media.map((media, index) => (
                <div key={index} className="border rounded-lg overflow-hidden">
                  {media.type === 'image' && (
                    <div>
                      <img
                        src={media.url}
                        alt={media.alt || media.caption}
                        className="w-full h-48 object-cover"
                      />
                      {media.caption && (
                        <p className="p-3 text-sm text-gray-600">{media.caption}</p>
                      )}
                    </div>
                  )}
                  {media.type === 'video' && media.embedCode && (
                    <div dangerouslySetInnerHTML={{ __html: media.embedCode }} />
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Tags */}
        {article.tags && article.tags.length > 0 && (
          <div className="mt-8 pt-6 border-t">
            <div className="flex items-center gap-2 mb-3">
              <Tag className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">Tags:</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {article.tags.map((tag) => (
                <span
                  key={tag}
                  className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm hover:bg-gray-200 cursor-pointer transition-colors"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* SEO Score (if available) */}
        {article.seoScore && (
          <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-green-800">SEO Score</span>
              <span className="text-lg font-bold text-green-600">{article.seoScore}/100</span>
            </div>
            <div className="mt-2 bg-green-200 rounded-full h-2">
              <div
                className="bg-green-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${article.seoScore}%` }}
              ></div>
            </div>
          </div>
        )}
      </div>
    </article>
  );
}

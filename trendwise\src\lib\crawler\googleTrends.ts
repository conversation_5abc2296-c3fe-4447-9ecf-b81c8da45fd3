import googleTrends from 'google-trends-api';
import { TrendingTopic } from '@/types';

export async function fetchGoogleTrends(
  geo: string = 'US',
  timeframe: string = 'now 1-d'
): Promise<TrendingTopic[]> {
  try {
    // Get daily trending searches
    const dailyTrends = await googleTrends.dailyTrends({
      trendDate: new Date(),
      geo: geo,
    });

    const parsedData = JSON.parse(dailyTrends);
    const trends: TrendingTopic[] = [];

    if (parsedData.default?.trendingSearchesDays?.[0]?.trendingSearches) {
      const trendingSearches = parsedData.default.trendingSearchesDays[0].trendingSearches;
      
      for (const trend of trendingSearches.slice(0, 10)) { // Get top 10 trends
        const keyword = trend.title?.query || '';
        if (keyword) {
          // Get related queries for each trend
          const relatedQueries = await getRelatedQueries(keyword, geo);
          
          trends.push({
            keyword,
            searchVolume: trend.formattedTraffic ? parseInt(trend.formattedTraffic.replace(/[^\d]/g, '')) || 0 : 0,
            relatedQueries,
            source: 'google',
            region: geo,
            timeframe
          });
        }
      }
    }

    return trends;
  } catch (error) {
    console.error('Error fetching Google Trends:', error);
    return [];
  }
}

async function getRelatedQueries(keyword: string, geo: string = 'US'): Promise<string[]> {
  try {
    const relatedQueries = await googleTrends.relatedQueries({
      keyword,
      geo,
      hl: 'en-US',
      category: 0,
      startTime: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
      endTime: new Date(),
    });

    const parsedData = JSON.parse(relatedQueries);
    const queries: string[] = [];

    if (parsedData.default?.rankedList?.[0]?.rankedKeyword) {
      const rankedKeywords = parsedData.default.rankedList[0].rankedKeyword;
      queries.push(...rankedKeywords.slice(0, 5).map((item: any) => item.query));
    }

    return queries;
  } catch (error) {
    console.error('Error fetching related queries:', error);
    return [];
  }
}

export async function searchGoogleTrends(keyword: string, geo: string = 'US'): Promise<any> {
  try {
    const interestOverTime = await googleTrends.interestOverTime({
      keyword,
      geo,
      hl: 'en-US',
      category: 0,
      startTime: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      endTime: new Date(),
    });

    return JSON.parse(interestOverTime);
  } catch (error) {
    console.error('Error searching Google Trends:', error);
    return null;
  }
}

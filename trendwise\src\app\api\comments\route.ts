import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Comment from '@/lib/models/Comment';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// GET /api/comments - Fetch comments for an article
export async function GET(request: NextRequest) {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const articleId = searchParams.get('articleId');
    
    if (!articleId) {
      return NextResponse.json(
        { error: 'Article ID is required' },
        { status: 400 }
      );
    }
    
    const comments = await Comment.find({ 
      articleId,
      isApproved: true 
    })
    .sort({ createdAt: -1 })
    .lean();
    
    // Organize comments into threads (parent comments with their replies)
    const parentComments = comments.filter(comment => !comment.parentId);
    const commentThreads = parentComments.map(parent => ({
      ...parent,
      replies: comments.filter(comment => comment.parentId === parent._id.toString())
    }));
    
    return NextResponse.json(commentThreads);
  } catch (error) {
    console.error('Error fetching comments:', error);
    return NextResponse.json(
      { error: 'Failed to fetch comments' },
      { status: 500 }
    );
  }
}

// POST /api/comments - Create new comment
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    await connectDB();
    
    const body = await request.json();
    const { articleId, content, parentId } = body;
    
    if (!articleId || !content) {
      return NextResponse.json(
        { error: 'Article ID and content are required' },
        { status: 400 }
      );
    }
    
    if (content.length > 1000) {
      return NextResponse.json(
        { error: 'Comment is too long (max 1000 characters)' },
        { status: 400 }
      );
    }
    
    const comment = new Comment({
      articleId,
      userId: session.user.id,
      userEmail: session.user.email,
      userName: session.user.name,
      userImage: session.user.image,
      content: content.trim(),
      parentId: parentId || null,
      isApproved: true // Auto-approve for now, can add moderation later
    });
    
    await comment.save();
    
    return NextResponse.json(comment, { status: 201 });
  } catch (error) {
    console.error('Error creating comment:', error);
    return NextResponse.json(
      { error: 'Failed to create comment' },
      { status: 500 }
    );
  }
}

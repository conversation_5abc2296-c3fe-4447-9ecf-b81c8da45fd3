'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Calendar, Eye } from 'lucide-react';
import { Article } from '@/types';

interface Props {
  currentArticleId: string;
  category: string;
  tags: string[];
}

export default function RelatedArticles({ currentArticleId, category, tags }: Props) {
  const [articles, setArticles] = useState<Article[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchRelatedArticles();
  }, [currentArticleId, category]);

  const fetchRelatedArticles = async () => {
    try {
      // Try to fetch related articles from the same category
      const response = await fetch(`/api/articles?category=${category}&limit=3`);
      if (response.ok) {
        const data = await response.json();
        // Filter out the current article
        const relatedArticles = data.articles.filter(
          (article: Article) => article._id !== currentArticleId
        );
        setArticles(relatedArticles.slice(0, 3));
      } else {
        // Fallback to sample articles
        setArticles(getSampleRelatedArticles());
      }
    } catch (error) {
      console.error('Error fetching related articles:', error);
      setArticles(getSampleRelatedArticles());
    } finally {
      setLoading(false);
    }
  };

  const getSampleRelatedArticles = (): Article[] => [
    {
      _id: 'related-1',
      title: 'Understanding Machine Learning Fundamentals',
      slug: 'machine-learning-fundamentals',
      excerpt: 'A comprehensive guide to getting started with machine learning concepts and applications...',
      content: '',
      metaDescription: 'Learn machine learning basics',
      metaKeywords: ['machine learning', 'AI', 'technology'],
      ogTitle: 'Machine Learning Fundamentals',
      ogDescription: 'Learn ML basics',
      featuredImage: 'https://via.placeholder.com/300x200?text=ML+Fundamentals',
      media: [],
      tags: ['AI', 'Machine Learning'],
      category: 'Technology',
      author: 'TrendWise AI',
      publishedAt: new Date(),
      updatedAt: new Date(),
      views: 856,
      isPublished: true
    },
    {
      _id: 'related-2',
      title: 'The Rise of Quantum Computing',
      slug: 'quantum-computing-rise',
      excerpt: 'Exploring the potential of quantum computing and its impact on various industries...',
      content: '',
      metaDescription: 'Quantum computing trends and impact',
      metaKeywords: ['quantum computing', 'technology', 'future'],
      ogTitle: 'The Rise of Quantum Computing',
      ogDescription: 'Quantum computing impact',
      featuredImage: 'https://via.placeholder.com/300x200?text=Quantum+Computing',
      media: [],
      tags: ['Quantum', 'Technology'],
      category: 'Technology',
      author: 'TrendWise AI',
      publishedAt: new Date(),
      updatedAt: new Date(),
      views: 1024,
      isPublished: true
    },
    {
      _id: 'related-3',
      title: 'Cybersecurity in the Digital Age',
      slug: 'cybersecurity-digital-age',
      excerpt: 'Essential cybersecurity practices for individuals and businesses in today\'s digital world...',
      content: '',
      metaDescription: 'Cybersecurity best practices',
      metaKeywords: ['cybersecurity', 'digital', 'security'],
      ogTitle: 'Cybersecurity in the Digital Age',
      ogDescription: 'Digital security practices',
      featuredImage: 'https://via.placeholder.com/300x200?text=Cybersecurity',
      media: [],
      tags: ['Security', 'Technology'],
      category: 'Technology',
      author: 'TrendWise AI',
      publishedAt: new Date(),
      updatedAt: new Date(),
      views: 743,
      isPublished: true
    }
  ];

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <section className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Related Articles</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="h-40 bg-gray-300 rounded-lg mb-4"></div>
              <div className="h-4 bg-gray-300 rounded mb-2"></div>
              <div className="h-4 bg-gray-300 rounded mb-2 w-3/4"></div>
              <div className="h-3 bg-gray-300 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </section>
    );
  }

  if (articles.length === 0) {
    return null;
  }

  return (
    <section className="bg-white rounded-lg shadow-lg p-6">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">Related Articles</h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {articles.map((article) => (
          <article key={article._id} className="group">
            <Link href={`/article/${article.slug}`}>
              <div className="relative h-40 bg-gray-200 rounded-lg overflow-hidden mb-4">
                {article.featuredImage ? (
                  <img
                    src={article.featuredImage}
                    alt={article.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-gray-400">
                    <span>No Image</span>
                  </div>
                )}
                <div className="absolute top-2 left-2">
                  <span className="bg-blue-600 text-white px-2 py-1 rounded text-xs font-medium">
                    {article.category}
                  </span>
                </div>
              </div>
            </Link>
            
            <div>
              <Link href={`/article/${article.slug}`}>
                <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors line-clamp-2">
                  {article.title}
                </h3>
              </Link>
              
              <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                {article.excerpt}
              </p>
              
              <div className="flex items-center justify-between text-xs text-gray-500">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center">
                    <Calendar className="h-3 w-3 mr-1" />
                    {formatDate(article.publishedAt)}
                  </div>
                  <div className="flex items-center">
                    <Eye className="h-3 w-3 mr-1" />
                    {article.views}
                  </div>
                </div>
              </div>
              
              <div className="mt-2 flex flex-wrap gap-1">
                {article.tags.slice(0, 2).map((tag) => (
                  <span
                    key={tag}
                    className="bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          </article>
        ))}
      </div>
    </section>
  );
}

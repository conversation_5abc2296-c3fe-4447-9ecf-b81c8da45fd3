import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import Header from '@/components/Header';
import ArticleContent from '@/components/ArticleContent';
import CommentSection from '@/components/CommentSection';
import RelatedArticles from '@/components/RelatedArticles';

interface Props {
  params: { slug: string };
}

async function getArticle(slug: string) {
  try {
    const response = await fetch(`${process.env.NEXTAUTH_URL}/api/articles/${slug}`, {
      cache: 'no-store' // Ensure fresh data for view count
    });
    
    if (!response.ok) {
      return null;
    }
    
    return response.json();
  } catch (error) {
    console.error('Error fetching article:', error);
    return null;
  }
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const article = await getArticle(params.slug);
  
  if (!article) {
    return {
      title: 'Article Not Found - TrendWise',
      description: 'The requested article could not be found.'
    };
  }
  
  return {
    title: `${article.title} - TrendWise`,
    description: article.metaDescription,
    keywords: article.metaKeywords.join(', '),
    openGraph: {
      title: article.ogTitle,
      description: article.ogDescription,
      images: article.ogImage ? [article.ogImage] : [],
      type: 'article',
      publishedTime: article.publishedAt,
      modifiedTime: article.updatedAt,
      authors: [article.author],
      tags: article.tags,
    },
    twitter: {
      card: 'summary_large_image',
      title: article.ogTitle,
      description: article.ogDescription,
      images: article.ogImage ? [article.ogImage] : [],
    },
    alternates: {
      canonical: `/article/${article.slug}`
    }
  };
}

export default async function ArticlePage({ params }: Props) {
  const article = await getArticle(params.slug);
  
  if (!article) {
    notFound();
  }
  
  // Generate JSON-LD structured data for SEO
  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: article.title,
    description: article.metaDescription,
    image: article.featuredImage,
    author: {
      '@type': 'Person',
      name: article.author,
    },
    publisher: {
      '@type': 'Organization',
      name: 'TrendWise',
      logo: {
        '@type': 'ImageObject',
        url: '/logo.png',
      },
    },
    datePublished: article.publishedAt,
    dateModified: article.updatedAt,
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': `/article/${article.slug}`,
    },
  };
  
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      
      <div className="min-h-screen bg-gray-50">
        <Header />
        
        <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <ArticleContent article={article} />
          <CommentSection articleId={article._id} />
        </main>
        
        <aside className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <RelatedArticles 
            currentArticleId={article._id} 
            category={article.category}
            tags={article.tags}
          />
        </aside>
      </div>
    </>
  );
}

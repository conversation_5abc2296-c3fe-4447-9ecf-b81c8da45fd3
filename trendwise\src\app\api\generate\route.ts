import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { fetchGoogleTrends } from '@/lib/crawler/googleTrends';
import { scrapeGoogleNews } from '@/lib/crawler/webScraper';
import { generateArticleContent, generateMetaTags } from '@/lib/openai';
import connectDB from '@/lib/mongodb';
import Article from '@/lib/models/Article';
import { generateSlug, ensureUniqueSlug } from '@/utils/slug';

// POST /api/generate - Generate article from trending topics
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const body = await request.json();
    const { keyword, category = 'Technology', autoPublish = false } = body;
    
    if (!keyword) {
      return NextResponse.json(
        { error: 'Keyword is required' },
        { status: 400 }
      );
    }
    
    // Step 1: Get trending data
    console.log('Fetching trending data for:', keyword);
    const trends = await fetchGoogleTrends();
    const relevantTrend = trends.find(t => 
      t.keyword.toLowerCase().includes(keyword.toLowerCase()) ||
      t.relatedQueries.some(q => q.toLowerCase().includes(keyword.toLowerCase()))
    ) || {
      keyword,
      searchVolume: 1000,
      relatedQueries: [keyword, `${keyword} news`, `${keyword} trends`],
      source: 'manual' as const,
      region: 'US'
    };
    
    // Step 2: Scrape additional context
    console.log('Scraping additional context...');
    const newsContext = await scrapeGoogleNews(keyword);
    const additionalContext = `
Recent news: ${newsContext.relatedArticles.join(', ')}
Description: ${newsContext.description}
`;
    
    // Step 3: Generate content using OpenAI
    console.log('Generating article content...');
    const generatedContent = await generateArticleContent(relevantTrend, additionalContext);
    
    // Step 4: Generate meta tags
    console.log('Generating meta tags...');
    const metaTags = await generateMetaTags(generatedContent.title, generatedContent.content);
    
    // Step 5: Create article in database
    await connectDB();
    
    const baseSlug = generateSlug(generatedContent.title);
    const existingSlugs = await Article.find({}, 'slug').then(articles => 
      articles.map(a => a.slug)
    );
    const uniqueSlug = ensureUniqueSlug(baseSlug, existingSlugs);
    
    const excerpt = generatedContent.content
      .replace(/#{1,6}\s/g, '') // Remove markdown headers
      .replace(/\*\*/g, '') // Remove bold markdown
      .substring(0, 300)
      .trim() + '...';
    
    const articleData = {
      title: generatedContent.title,
      slug: uniqueSlug,
      content: generatedContent.content,
      excerpt,
      metaDescription: generatedContent.metaDescription,
      metaKeywords: generatedContent.keywords,
      ogTitle: metaTags.ogTitle,
      ogDescription: metaTags.ogDescription,
      ogImage: generatedContent.media[0]?.url,
      featuredImage: generatedContent.media[0]?.url,
      media: generatedContent.media,
      tags: generatedContent.keywords.slice(0, 5),
      category,
      author: 'TrendWise AI',
      isPublished: autoPublish,
      seoScore: calculateSEOScore(generatedContent)
    };
    
    const article = new Article(articleData);
    await article.save();
    
    console.log('Article generated successfully:', article.slug);
    
    return NextResponse.json({
      success: true,
      article: {
        id: article._id,
        title: article.title,
        slug: article.slug,
        excerpt: article.excerpt,
        isPublished: article.isPublished,
        seoScore: article.seoScore
      },
      message: autoPublish ? 'Article generated and published' : 'Article generated as draft'
    });
    
  } catch (error) {
    console.error('Error generating article:', error);
    return NextResponse.json(
      { error: 'Failed to generate article', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

function calculateSEOScore(content: any): number {
  let score = 0;
  
  // Title length (50-60 chars is optimal)
  const titleLength = content.title.length;
  if (titleLength >= 50 && titleLength <= 60) score += 20;
  else if (titleLength >= 40 && titleLength <= 70) score += 15;
  else score += 10;
  
  // Meta description length (150-160 chars is optimal)
  const metaLength = content.metaDescription.length;
  if (metaLength >= 150 && metaLength <= 160) score += 20;
  else if (metaLength >= 120 && metaLength <= 170) score += 15;
  else score += 10;
  
  // Content length (1500+ words is good)
  const wordCount = content.content.split(' ').length;
  if (wordCount >= 1500) score += 20;
  else if (wordCount >= 1000) score += 15;
  else score += 10;
  
  // Keywords count
  if (content.keywords.length >= 5) score += 15;
  else score += 10;
  
  // Headings structure
  if (content.headings.length >= 3) score += 15;
  else score += 10;
  
  // Media presence
  if (content.media.length > 0) score += 10;
  
  return Math.min(score, 100);
}

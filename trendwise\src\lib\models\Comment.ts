import mongoose, { Schema, Document } from 'mongoose';
import { Comment as IComment } from '@/types';

const CommentSchema = new Schema<IComment & Document>({
  articleId: {
    type: String,
    required: true,
    ref: 'Article'
  },
  userId: {
    type: String,
    required: true
  },
  userEmail: {
    type: String,
    required: true,
    trim: true,
    lowercase: true
  },
  userName: {
    type: String,
    required: true,
    trim: true
  },
  userImage: String,
  content: {
    type: String,
    required: true,
    trim: true,
    maxlength: 1000
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  isApproved: {
    type: Boolean,
    default: true
  },
  parentId: {
    type: String,
    ref: 'Comment'
  }
}, {
  timestamps: true
});

// Create indexes
CommentSchema.index({ articleId: 1, createdAt: -1 });
CommentSchema.index({ userId: 1 });
CommentSchema.index({ parentId: 1 });

export default mongoose.models.Comment || mongoose.model<IComment & Document>('Comment', CommentSchema);

'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Calendar, Eye, Clock, Search } from 'lucide-react';
import { Article } from '@/types';

export default function ArticleList() {
  const [articles, setArticles] = useState<Article[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const categories = ['Technology', 'Business', 'Health', 'Entertainment', 'Sports', 'Politics'];

  useEffect(() => {
    fetchArticles();
  }, [currentPage, selectedCategory, searchTerm]);

  const fetchArticles = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '9'
      });
      
      if (selectedCategory) params.append('category', selectedCategory);
      if (searchTerm) params.append('search', searchTerm);
      
      const response = await fetch(`/api/articles?${params}`);
      const data = await response.json();
      
      if (response.ok) {
        setArticles(data.articles);
        setTotalPages(data.pagination.pages);
      } else {
        console.error('Failed to fetch articles:', data.error);
        // Show sample articles if API fails
        setArticles(getSampleArticles());
      }
    } catch (error) {
      console.error('Error fetching articles:', error);
      // Show sample articles if API fails
      setArticles(getSampleArticles());
    } finally {
      setLoading(false);
    }
  };

  const getSampleArticles = (): Article[] => [
    {
      _id: '1',
      title: 'The Future of Artificial Intelligence in 2024',
      slug: 'future-of-ai-2024',
      excerpt: 'Explore the latest developments in AI technology and what to expect in the coming year...',
      content: '',
      metaDescription: 'Latest AI trends and developments for 2024',
      metaKeywords: ['AI', 'technology', 'future'],
      ogTitle: 'The Future of AI in 2024',
      ogDescription: 'Latest AI trends and developments',
      featuredImage: 'https://via.placeholder.com/400x250?text=AI+Future',
      media: [],
      tags: ['AI', 'Technology'],
      category: 'Technology',
      author: 'TrendWise AI',
      publishedAt: new Date(),
      updatedAt: new Date(),
      views: 1250,
      isPublished: true
    },
    {
      _id: '2',
      title: 'Climate Change Solutions: Innovative Technologies',
      slug: 'climate-change-solutions-2024',
      excerpt: 'Discover groundbreaking technologies that are helping combat climate change...',
      content: '',
      metaDescription: 'Innovative climate change solutions and technologies',
      metaKeywords: ['climate', 'technology', 'environment'],
      ogTitle: 'Climate Change Solutions',
      ogDescription: 'Innovative technologies for climate action',
      featuredImage: 'https://via.placeholder.com/400x250?text=Climate+Tech',
      media: [],
      tags: ['Climate', 'Technology'],
      category: 'Technology',
      author: 'TrendWise AI',
      publishedAt: new Date(),
      updatedAt: new Date(),
      views: 890,
      isPublished: true
    },
    {
      _id: '3',
      title: 'Remote Work Trends: The New Normal',
      slug: 'remote-work-trends-2024',
      excerpt: 'How remote work is reshaping the business landscape and what it means for the future...',
      content: '',
      metaDescription: 'Remote work trends and business impact',
      metaKeywords: ['remote work', 'business', 'trends'],
      ogTitle: 'Remote Work Trends 2024',
      ogDescription: 'The future of remote work',
      featuredImage: 'https://via.placeholder.com/400x250?text=Remote+Work',
      media: [],
      tags: ['Business', 'Work'],
      category: 'Business',
      author: 'TrendWise AI',
      publishedAt: new Date(),
      updatedAt: new Date(),
      views: 675,
      isPublished: true
    }
  ];

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchArticles();
  };

  return (
    <div>
      {/* Search and Filter Section */}
      <div className="mb-8">
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <form onSubmit={handleSearch} className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="Search articles..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </form>
          
          <select
            value={selectedCategory}
            onChange={(e) => {
              setSelectedCategory(e.target.value);
              setCurrentPage(1);
            }}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Categories</option>
            {categories.map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>
        </div>
      </div>

      {/* Articles Grid */}
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-8">Latest Articles</h2>
        
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="bg-white rounded-lg shadow-md overflow-hidden animate-pulse">
                <div className="h-48 bg-gray-300"></div>
                <div className="p-6">
                  <div className="h-4 bg-gray-300 rounded mb-2"></div>
                  <div className="h-4 bg-gray-300 rounded mb-4 w-3/4"></div>
                  <div className="h-3 bg-gray-300 rounded mb-2"></div>
                  <div className="h-3 bg-gray-300 rounded mb-2"></div>
                  <div className="h-3 bg-gray-300 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {articles.map((article) => (
              <article key={article._id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                <Link href={`/article/${article.slug}`}>
                  <div className="relative h-48 bg-gray-200">
                    {article.featuredImage ? (
                      <img
                        src={article.featuredImage}
                        alt={article.title}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-gray-400">
                        <span>No Image</span>
                      </div>
                    )}
                    <div className="absolute top-4 left-4">
                      <span className="bg-blue-600 text-white px-2 py-1 rounded text-sm font-medium">
                        {article.category}
                      </span>
                    </div>
                  </div>
                </Link>
                
                <div className="p-6">
                  <Link href={`/article/${article.slug}`}>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2 hover:text-blue-600 transition-colors line-clamp-2">
                      {article.title}
                    </h3>
                  </Link>
                  
                  <p className="text-gray-600 mb-4 line-clamp-3">
                    {article.excerpt}
                  </p>
                  
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        {formatDate(article.publishedAt)}
                      </div>
                      <div className="flex items-center">
                        <Eye className="h-4 w-4 mr-1" />
                        {article.views}
                      </div>
                    </div>
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      5 min read
                    </div>
                  </div>
                  
                  <div className="mt-4 flex flex-wrap gap-2">
                    {article.tags.slice(0, 3).map((tag) => (
                      <span
                        key={tag}
                        className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              </article>
            ))}
          </div>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center space-x-2">
          <button
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
            className="px-4 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            Previous
          </button>
          
          {[...Array(totalPages)].map((_, i) => (
            <button
              key={i + 1}
              onClick={() => setCurrentPage(i + 1)}
              className={`px-4 py-2 border rounded-lg ${
                currentPage === i + 1
                  ? 'bg-blue-600 text-white border-blue-600'
                  : 'border-gray-300 hover:bg-gray-50'
              }`}
            >
              {i + 1}
            </button>
          ))}
          
          <button
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
            className="px-4 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            Next
          </button>
        </div>
      )}
    </div>
  );
}

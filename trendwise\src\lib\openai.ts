import OpenAI from 'openai';
import { GeneratedContent, TrendingTopic, MediaItem } from '@/types';
import { generateSlug } from '@/utils/slug';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function generateArticleContent(
  topic: TrendingTopic,
  additionalContext?: string
): Promise<GeneratedContent> {
  try {
    const prompt = createArticlePrompt(topic, additionalContext);
    
    const completion = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: "You are an expert SEO content writer and journalist. Create engaging, well-structured, and SEO-optimized articles that are informative and engaging for readers."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      max_tokens: 3000,
      temperature: 0.7,
    });

    const content = completion.choices[0]?.message?.content || '';
    
    // Parse the generated content
    const parsedContent = parseGeneratedContent(content, topic.keyword);
    
    return parsedContent;
  } catch (error) {
    console.error('Error generating article content:', error);
    throw new Error('Failed to generate article content');
  }
}

function createArticlePrompt(topic: TrendingTopic, additionalContext?: string): string {
  return `
Write a comprehensive, SEO-optimized article about "${topic.keyword}".

Requirements:
1. Create an engaging title (50-60 characters)
2. Write a compelling meta description (150-160 characters)
3. Structure the article with proper H1, H2, and H3 headings
4. Include 1500-2000 words of high-quality content
5. Use the keyword naturally throughout the content
6. Include related keywords: ${topic.relatedQueries.join(', ')}
7. Make it informative, engaging, and valuable to readers
8. Include a conclusion with key takeaways

${additionalContext ? `Additional context: ${additionalContext}` : ''}

Format your response as follows:
TITLE: [Your title here]
META_DESCRIPTION: [Your meta description here]
KEYWORDS: [comma-separated list of 5-10 keywords]

CONTENT:
[Your full article content with proper headings and structure]

HEADINGS: [List all H2 and H3 headings used in the article]
`;
}

function parseGeneratedContent(content: string, keyword: string): GeneratedContent {
  const lines = content.split('\n');
  let title = '';
  let metaDescription = '';
  let keywords: string[] = [];
  let articleContent = '';
  let headings: string[] = [];
  let isContentSection = false;
  let isHeadingsSection = false;

  for (const line of lines) {
    const trimmedLine = line.trim();
    
    if (trimmedLine.startsWith('TITLE:')) {
      title = trimmedLine.replace('TITLE:', '').trim();
    } else if (trimmedLine.startsWith('META_DESCRIPTION:')) {
      metaDescription = trimmedLine.replace('META_DESCRIPTION:', '').trim();
    } else if (trimmedLine.startsWith('KEYWORDS:')) {
      const keywordString = trimmedLine.replace('KEYWORDS:', '').trim();
      keywords = keywordString.split(',').map(k => k.trim()).filter(k => k.length > 0);
    } else if (trimmedLine === 'CONTENT:') {
      isContentSection = true;
      isHeadingsSection = false;
    } else if (trimmedLine.startsWith('HEADINGS:')) {
      isContentSection = false;
      isHeadingsSection = true;
      const headingString = trimmedLine.replace('HEADINGS:', '').trim();
      if (headingString) {
        headings = headingString.split(',').map(h => h.trim()).filter(h => h.length > 0);
      }
    } else if (isContentSection && trimmedLine !== 'HEADINGS:') {
      articleContent += line + '\n';
    } else if (isHeadingsSection) {
      if (trimmedLine && !trimmedLine.startsWith('HEADINGS:')) {
        headings.push(trimmedLine);
      }
    }
  }

  // Fallback values if parsing fails
  if (!title) {
    title = `Everything You Need to Know About ${keyword}`;
  }
  if (!metaDescription) {
    metaDescription = `Discover the latest insights and trends about ${keyword}. Stay informed with our comprehensive analysis and expert perspectives.`;
  }
  if (keywords.length === 0) {
    keywords = [keyword, ...keyword.split(' ').filter(w => w.length > 2)];
  }
  if (!articleContent.trim()) {
    articleContent = `# ${title}\n\nThis article provides comprehensive coverage of ${keyword} and related topics.\n\n## Overview\n\n${keyword} has been trending recently, and here's what you need to know...\n\n## Key Points\n\n- Important aspect 1\n- Important aspect 2\n- Important aspect 3\n\n## Conclusion\n\nIn conclusion, ${keyword} represents an important development that deserves attention.`;
  }

  // Generate placeholder media
  const media: MediaItem[] = [
    {
      type: 'image',
      url: `https://via.placeholder.com/800x400?text=${encodeURIComponent(keyword)}`,
      alt: `Image related to ${keyword}`,
      caption: `Visual representation of ${keyword}`
    }
  ];

  return {
    title: title.substring(0, 100), // Ensure title isn't too long
    content: articleContent.trim(),
    metaDescription: metaDescription.substring(0, 160), // Ensure meta description fits limit
    keywords,
    headings,
    media
  };
}

export async function generateMetaTags(title: string, content: string): Promise<{
  ogTitle: string;
  ogDescription: string;
  keywords: string[];
}> {
  try {
    const prompt = `
Based on this article title and content, generate SEO meta tags:

Title: ${title}
Content: ${content.substring(0, 500)}...

Please provide:
1. An engaging Open Graph title (50-60 characters)
2. An Open Graph description (150-160 characters)
3. 5-10 relevant keywords

Format:
OG_TITLE: [title]
OG_DESCRIPTION: [description]
KEYWORDS: [comma-separated keywords]
`;

    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "You are an SEO expert. Generate optimized meta tags for social media sharing."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      max_tokens: 300,
      temperature: 0.5,
    });

    const response = completion.choices[0]?.message?.content || '';
    
    // Parse response
    const ogTitleMatch = response.match(/OG_TITLE:\s*(.+)/);
    const ogDescMatch = response.match(/OG_DESCRIPTION:\s*(.+)/);
    const keywordsMatch = response.match(/KEYWORDS:\s*(.+)/);
    
    return {
      ogTitle: ogTitleMatch?.[1]?.trim() || title,
      ogDescription: ogDescMatch?.[1]?.trim() || content.substring(0, 160),
      keywords: keywordsMatch?.[1]?.split(',').map(k => k.trim()) || []
    };
  } catch (error) {
    console.error('Error generating meta tags:', error);
    return {
      ogTitle: title,
      ogDescription: content.substring(0, 160),
      keywords: []
    };
  }
}
